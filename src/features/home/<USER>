// Home Feature - Main Exports
// This file exports all public APIs for the home feature

// Configuration
export { homeFeatureConfig, getCurrentHomeConfig, getComponentVersion } from './config';

// Pages
export { HomePage } from './pages';

// Components (for external use)
export { default as HeroSection } from './components/hero';
export { BreakingNews, UpcomingFixtures } from './components/content-sections';
export { EngagementFeatures } from './components/engagement-features';
export { FooterContent } from './components/footer-content';

// Types
export type { HomeFeatureConfig } from './config';
export type * from './types';
