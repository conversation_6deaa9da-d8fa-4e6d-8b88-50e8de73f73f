# URL Structure Testing Guide

## Test URLs for New Structure

### 1. **All Articles**
```
✅ http://localhost:5000/news
✅ http://localhost:5000/news?page=2
✅ http://localhost:5000/news?search=transfer
✅ http://localhost:5000/news?search=transfer&page=2
```

### 2. **Category Articles**
```
✅ http://localhost:5000/news-category/football
✅ http://localhost:5000/news-category/football?page=2
✅ http://localhost:5000/news-category/transfer
✅ http://localhost:5000/news-category/transfer?page=2
✅ http://localhost:5000/news-category/football?search=messi
✅ http://localhost:5000/news-category/football?search=messi&page=2
```

### 3. **Article Details**
```
✅ http://localhost:5000/news/b-quy-nh-mi-gia-nh-ch-c-sinh-ti-a-hai-con
✅ http://localhost:5000/news/iu-g-xy-ra-khi-n-ni-tng-tht-ti-sng
```

## Testing Checklist

### ✅ **Navigation Flow**
1. **Homepage → News**: Click "News" → Should go to `/news`
2. **News → Category**: Click "Football" → Should go to `/news-category/football`
3. **Category → All**: Click "All Categories" → Should go to `/news`
4. **Category → Category**: Click "Transfer" → Should go to `/news-category/transfer`

### ✅ **Pagination**
1. **All Articles**: `/news` → Page 2 → `/news?page=2`
2. **Category Articles**: `/news-category/football` → Page 2 → `/news-category/football?page=2`

### ✅ **Search**
1. **All Articles**: Search "transfer" → `/news?search=transfer`
2. **Category Articles**: Search "messi" in football → `/news-category/football?search=messi`

### ✅ **Article Detail Navigation**
1. **From All**: Click article → `/news/{slug}`
2. **From Category**: Click article → `/news/{slug}`
3. **Category Badge**: Click category → `/news-category/{category}`
4. **Breadcrumb**: Click category → `/news-category/{category}`

### ✅ **URL Consistency**
1. **Clean URLs**: No unnecessary parameters
2. **Readable URLs**: Human-friendly structure
3. **SEO Friendly**: Proper URL hierarchy

## Expected Behaviors

### 1. **Category Sidebar**
- **Active State**: Highlights current category
- **Navigation**: Smooth transitions between categories
- **Consistency**: Same sidebar on all pages

### 2. **Pagination**
- **Number-based**: Shows page numbers (1, 2, 3...)
- **10 per page**: Exactly 10 articles per page
- **URL Updates**: Page parameter in URL
- **Scroll to Top**: Auto-scroll on page change

### 3. **Search**
- **URL State**: Search query in URL
- **Persistence**: Search persists across navigation
- **Category Scope**: Search within category when applicable

### 4. **Article Detail**
- **Consistent Layout**: Same sidebar as list pages
- **Category Context**: Shows current category
- **Navigation**: Easy return to category/all articles

## Performance Expectations

### 1. **Loading Times**
- **Initial Load**: < 2 seconds
- **Navigation**: < 500ms (shared layout)
- **Pagination**: < 300ms

### 2. **SEO**
- **Meta Tags**: Dynamic titles and descriptions
- **Structured Data**: Rich snippets
- **Open Graph**: Social sharing optimization

### 3. **Responsive**
- **Mobile**: Touch-friendly navigation
- **Tablet**: Optimized layout
- **Desktop**: Full feature set

## Common Issues to Check

### 1. **URL Issues**
- ❌ Double slashes: `/news//category`
- ❌ Missing parameters: `/news-category/`
- ❌ Wrong encoding: Special characters

### 2. **Navigation Issues**
- ❌ Broken category links
- ❌ Incorrect pagination URLs
- ❌ Search not working

### 3. **Layout Issues**
- ❌ Sidebar not sticky
- ❌ Content overflow
- ❌ Responsive problems

### 4. **Performance Issues**
- ❌ Slow loading
- ❌ Layout shifts
- ❌ Memory leaks

## Browser Testing

### ✅ **Desktop Browsers**
- Chrome/Edge (Latest)
- Firefox (Latest)
- Safari (Latest)

### ✅ **Mobile Browsers**
- Chrome Mobile
- Safari Mobile
- Samsung Internet

### ✅ **Features to Test**
- Back/Forward navigation
- Bookmark functionality
- Share URLs
- Refresh behavior

## Debugging Commands

### 1. **Check Routes**
```bash
# Check if server is running
curl http://localhost:5000/news

# Check category route
curl http://localhost:5000/news-category/football

# Check article route
curl http://localhost:5000/news/test-article
```

### 2. **Check API Endpoints**
```bash
# Check news API
curl http://localhost:5000/api/news

# Check category API
curl http://localhost:5000/api/news/category/football

# Check article API
curl http://localhost:5000/api/news/article/test-slug
```

### 3. **Check Console Logs**
- Open browser DevTools
- Check Console for errors
- Monitor Network tab for API calls
- Check Application tab for caching

## Success Criteria

### ✅ **Functional**
1. All URLs work correctly
2. Navigation flows smoothly
3. Pagination works properly
4. Search functionality works
5. Article details display correctly

### ✅ **Performance**
1. Fast loading times
2. Smooth transitions
3. Efficient caching
4. No memory leaks

### ✅ **SEO**
1. Proper meta tags
2. Clean URL structure
3. Structured data
4. Social sharing works

### ✅ **UX**
1. Intuitive navigation
2. Consistent layout
3. Responsive design
4. Accessible interface

## Test Results Template

```
Date: ___________
Tester: ___________

URL Structure Tests:
□ /news - Works
□ /news?page=2 - Works
□ /news-category/football - Works
□ /news-category/football?page=2 - Works
□ /news/{slug} - Works

Navigation Tests:
□ Category switching - Works
□ Pagination - Works
□ Search - Works
□ Article detail - Works

Performance Tests:
□ Loading speed - Acceptable
□ Navigation speed - Fast
□ Memory usage - Normal

Issues Found:
1. ___________
2. ___________
3. ___________

Overall Status: ✅ PASS / ❌ FAIL
```

Use this guide to thoroughly test the new URL structure and ensure everything works as expected.
