// Home Pages - Version Management
// This file manages all page-level components for the home feature

import { homeFeatureConfig } from '../config';

// Page exports based on configuration
export { default as HomePage } from './home-page';

// Dynamic imports for version switching
export const getHomePage = () => {
  const version = homeFeatureConfig.pages.homePage;
  return import(`./home-page/v${version}`).then(m => m.default);
};

// Version mapping for dynamic imports
export const HOME_PAGE_VERSIONS = {
  v1: () => import('./home-page/v1').then(m => m.default),
  // v2: () => import('./home-page/v2').then(m => m.default),
} as const;

// Helper function to get page version
export const getHomePageVersion = (version: keyof typeof HOME_PAGE_VERSIONS = 'v1') => {
  return HOME_PAGE_VERSIONS[version];
};
