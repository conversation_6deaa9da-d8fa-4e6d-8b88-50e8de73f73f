'use client';

import { NewsArticle, NewsAPIResponse } from '@/lib/api/news';
import { NewsCategory, NewsCategoriesAPIResponse } from '@/lib/api/newsCategories';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries
}

/**
 * News Cache Service - Optimized caching for news data
 * 
 * Features:
 * - TTL-based cache expiration
 * - Memory management with max size
 * - Separate caches for different data types
 * - Cache invalidation strategies
 * - Performance monitoring
 */
export class NewsCacheService {
  private static instance: NewsCacheService;
  
  // Separate caches for different data types
  private articlesCache = new Map<string, CacheEntry<NewsAPIResponse>>();
  private categoriesCache = new Map<string, CacheEntry<NewsCategoriesAPIResponse>>();
  private relatedArticlesCache = new Map<string, CacheEntry<NewsArticle[]>>();
  
  // Cache configurations
  private readonly configs = {
    articles: { ttl: 5 * 60 * 1000, maxSize: 50 }, // 5 minutes, 50 entries
    categories: { ttl: 30 * 60 * 1000, maxSize: 10 }, // 30 minutes, 10 entries
    relatedArticles: { ttl: 10 * 60 * 1000, maxSize: 100 }, // 10 minutes, 100 entries
  };

  private constructor() {
    // Start cleanup interval
    this.startCleanupInterval();
  }

  public static getInstance(): NewsCacheService {
    if (!NewsCacheService.instance) {
      NewsCacheService.instance = new NewsCacheService();
    }
    return NewsCacheService.instance;
  }

  /**
   * Cache articles with automatic cleanup
   */
  public cacheArticles(key: string, data: NewsAPIResponse): void {
    this.setCache(this.articlesCache, key, data, this.configs.articles);
  }

  /**
   * Get cached articles
   */
  public getCachedArticles(key: string): NewsAPIResponse | null {
    return this.getCache(this.articlesCache, key);
  }

  /**
   * Cache categories with long TTL
   */
  public cacheCategories(key: string, data: NewsCategoriesAPIResponse): void {
    this.setCache(this.categoriesCache, key, data, this.configs.categories);
  }

  /**
   * Get cached categories
   */
  public getCachedCategories(key: string): NewsCategoriesAPIResponse | null {
    return this.getCache(this.categoriesCache, key);
  }

  /**
   * Cache related articles
   */
  public cacheRelatedArticles(key: string, data: NewsArticle[]): void {
    this.setCache(this.relatedArticlesCache, key, data, this.configs.relatedArticles);
  }

  /**
   * Get cached related articles
   */
  public getCachedRelatedArticles(key: string): NewsArticle[] | null {
    return this.getCache(this.relatedArticlesCache, key);
  }

  /**
   * Generic cache setter with TTL and size management
   */
  private setCache<T>(
    cache: Map<string, CacheEntry<T>>,
    key: string,
    data: T,
    config: CacheConfig
  ): void {
    const now = Date.now();
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + config.ttl,
    };

    // Remove expired entries if cache is full
    if (cache.size >= config.maxSize) {
      this.cleanupCache(cache);
      
      // If still full, remove oldest entry
      if (cache.size >= config.maxSize) {
        const oldestKey = Array.from(cache.keys())[0];
        cache.delete(oldestKey);
      }
    }

    cache.set(key, entry);
  }

  /**
   * Generic cache getter with expiration check
   */
  private getCache<T>(
    cache: Map<string, CacheEntry<T>>,
    key: string
  ): T | null {
    const entry = cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Clean up expired entries from a cache
   */
  private cleanupCache<T>(cache: Map<string, CacheEntry<T>>): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => cache.delete(key));
  }

  /**
   * Start periodic cleanup of all caches
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupCache(this.articlesCache);
      this.cleanupCache(this.categoriesCache);
      this.cleanupCache(this.relatedArticlesCache);
    }, 60 * 1000); // Cleanup every minute
  }

  /**
   * Clear all caches
   */
  public clearAll(): void {
    this.articlesCache.clear();
    this.categoriesCache.clear();
    this.relatedArticlesCache.clear();
  }

  /**
   * Clear specific cache type
   */
  public clearArticles(): void {
    this.articlesCache.clear();
  }

  public clearCategories(): void {
    this.categoriesCache.clear();
  }

  public clearRelatedArticles(): void {
    this.relatedArticlesCache.clear();
  }

  /**
   * Get cache statistics for monitoring
   */
  public getStats() {
    return {
      articles: {
        size: this.articlesCache.size,
        maxSize: this.configs.articles.maxSize,
        ttl: this.configs.articles.ttl,
      },
      categories: {
        size: this.categoriesCache.size,
        maxSize: this.configs.categories.maxSize,
        ttl: this.configs.categories.ttl,
      },
      relatedArticles: {
        size: this.relatedArticlesCache.size,
        maxSize: this.configs.relatedArticles.maxSize,
        ttl: this.configs.relatedArticles.ttl,
      },
    };
  }

  /**
   * Generate cache key for articles
   */
  public static generateArticlesKey(params: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
  }): string {
    const { page = 1, limit = 20, category = 'all', search = '' } = params;
    return `articles:${page}:${limit}:${category}:${search}`;
  }

  /**
   * Generate cache key for categories
   */
  public static generateCategoriesKey(): string {
    return 'categories:all';
  }

  /**
   * Generate cache key for related articles
   */
  public static generateRelatedKey(slug: string): string {
    return `related:${slug}`;
  }
}

// Export singleton instance
export const newsCacheService = NewsCacheService.getInstance();
