# News URL Structure Update - V2.1

## Overview
Đã cập nhật URL structure cho News Pages theo yêu cầu mới với dedicated routes cho categories và pagination.

## New URL Structure

### 1. **All Articles**
- **Main Page**: `/news` - Tất cả bài viết (trang 1)
- **Pagination**: `/news?page=2` - Tất cả bài viết (trang 2)
- **Search**: `/news?search=transfer` - Tìm kiếm trong tất cả bài viết
- **Combined**: `/news?search=transfer&page=2` - Tìm kiếm với pagination

### 2. **Category Articles**
- **Category Page**: `/news-category/football` - Bài viết football (trang 1)
- **Pagination**: `/news-category/football?page=2` - Bài viết football (trang 2)
- **Search**: `/news-category/football?search=messi` - Tìm kiếm trong category
- **Combined**: `/news-category/football?search=messi&page=2` - T<PERSON><PERSON> kiếm với pagination

### 3. **Article Detail**
- **Detail Page**: `/news/{slug}` - Chi tiết bài viết
- **Example**: `/news/messi-transfer-to-psg` - Bài viết cụ thể

## Implementation Details

### 1. **New Route File** (`src/app/news-category/[category]/page.tsx`)

#### Features:
- **Dynamic routing** cho categories
- **SEO optimized** với dynamic metadata
- **Static generation** cho known categories
- **Error handling** với fallback categories

#### Metadata Generation:
```typescript
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryName = await getCategoryName(category);
  
  return {
    title: `${categoryName} News | Latest Updates`,
    description: `Latest ${categoryName.toLowerCase()} news and updates`,
    // ... more SEO metadata
  };
}
```

#### Static Params Generation:
```typescript
export async function generateStaticParams() {
  const categoriesResponse = await fetchNewsCategories();
  return categoriesResponse.data.map((category) => ({
    category: category.slug,
  }));
}
```

### 2. **Updated NewsPageV2** (`src/features/news/pages/news-page/v2/NewsPageV2.tsx`)

#### New Props Interface:
```typescript
interface NewsPageV2Props {
  initialCategory?: string;        // From route params
  initialSearchParams?: object;    // From URL search params
}
```

#### Enhanced URL Logic:
```typescript
const updateUrl = useCallback((newCategory: string, newSearch: string, newPage: number = 1) => {
  // Determine base URL based on category
  let baseUrl: string;
  if (newCategory === 'all') {
    baseUrl = '/news';
  } else {
    baseUrl = `/news-category/${newCategory}`;
  }
  
  // Add query parameters
  const params = new URLSearchParams();
  if (newSearch) params.set('search', newSearch);
  if (newPage > 1) params.set('page', newPage.toString());
  
  const newUrl = params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
  router.push(newUrl, { scroll: false });
}, [router]);
```

### 3. **Updated NewsDetailPageV2** (`src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx`)

#### Navigation Updates:
```typescript
// Category navigation
const handleCategoryChange = useCallback((category: string) => {
  const url = category === 'all' ? '/news' : `/news-category/${category}`;
  router.push(url);
}, [router]);
```

#### Breadcrumb Updates:
```tsx
<Link href={`/news-category/${article.category.slug}`}>
  {article.category.name}
</Link>
```

#### Category Badge Updates:
```tsx
<Link href={`/news-category/${article.category.slug}`}>
  <span>{article.category.name}</span>
</Link>
```

## URL Examples

### Complete URL Structure:

#### All Articles:
```
/news                           # Page 1, all articles
/news?page=2                    # Page 2, all articles  
/news?search=transfer           # Search in all articles
/news?search=transfer&page=2    # Search with pagination
```

#### Category Articles:
```
/news-category/football                        # Football page 1
/news-category/football?page=2                 # Football page 2
/news-category/transfer                        # Transfer page 1
/news-category/transfer?page=3                 # Transfer page 3
/news-category/football?search=messi           # Search in football
/news-category/football?search=messi&page=2    # Search with pagination
```

#### Article Details:
```
/news/messi-transfer-to-psg                    # Article detail
/news/champions-league-final-preview           # Article detail
/news/premier-league-weekend-roundup           # Article detail
```

## SEO Benefits

### 1. **Clean URLs**
- **Category URLs**: `/news-category/football` instead of `/news?category=football`
- **Better readability** for users and search engines
- **Improved sharing** experience

### 2. **Dynamic Metadata**
- **Category-specific titles** and descriptions
- **Structured data** for rich snippets
- **Open Graph** optimization for social sharing

### 3. **Static Generation**
- **Pre-generated pages** for known categories
- **Faster loading** times
- **Better SEO** indexing

## Navigation Flow

### 1. **From Homepage**
```
Homepage → /news (All articles)
Homepage → /news-category/football (Direct category access)
```

### 2. **Category Navigation**
```
/news → Click "Football" → /news-category/football
/news-category/football → Click "All" → /news
/news-category/football → Click "Transfer" → /news-category/transfer
```

### 3. **Article Navigation**
```
/news-category/football → Click article → /news/{slug}
/news/{slug} → Click category badge → /news-category/football
/news/{slug} → Click breadcrumb → /news-category/football
```

### 4. **Pagination Navigation**
```
/news → Page 2 → /news?page=2
/news-category/football → Page 2 → /news-category/football?page=2
```

## Performance Optimizations

### 1. **Route-level Optimizations**
- **Static generation** for category pages
- **Dynamic metadata** generation
- **Efficient caching** strategies

### 2. **Component-level Optimizations**
- **Shared layout** component
- **Memoized callbacks** for URL updates
- **Optimized re-renders** with proper dependencies

### 3. **SEO Optimizations**
- **Canonical URLs** for duplicate content prevention
- **Structured data** for rich snippets
- **Meta tags** optimization

## Backward Compatibility

### 1. **Legacy URL Support**
- Old URLs with query parameters still work
- Automatic redirects to new structure (optional)
- Graceful fallback for unknown routes

### 2. **Component Compatibility**
- V1 components still available
- Easy switching via configuration
- No breaking changes to existing APIs

## Testing Strategy

### 1. **Route Testing**
- All URL patterns work correctly
- Proper parameter parsing
- Correct navigation flows

### 2. **SEO Testing**
- Metadata generation works
- Structured data is valid
- Open Graph tags are correct

### 3. **Performance Testing**
- Page load times
- Navigation speed
- Cache effectiveness

## Files Modified

### New Files:
- `src/app/news-category/[category]/page.tsx` - Category route handler

### Modified Files:
- `src/features/news/pages/news-page/v2/NewsPageV2.tsx` - URL logic updates
- `src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx` - Navigation updates

### Configuration:
- URL structure updated in routing logic
- SEO metadata enhanced
- Static generation configured

## Migration Notes

### For Developers:
1. **New URL structure** is now active
2. **Category links** use new format
3. **Navigation logic** updated throughout

### For Content:
1. **Existing URLs** continue to work
2. **SEO benefits** from cleaner URLs
3. **Better user experience** with readable URLs

### For Users:
1. **Cleaner URLs** for sharing
2. **Better navigation** experience
3. **Faster page loads** with static generation

The new URL structure provides better SEO, improved user experience, and cleaner, more maintainable code while maintaining full backward compatibility.
