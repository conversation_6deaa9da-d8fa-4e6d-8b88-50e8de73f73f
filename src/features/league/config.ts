// League Feature Configuration
// This file manages all version configurations for the league feature

export const leagueFeatureConfig = {
  // Page-level configurations
  pages: {
    leaguePage: 'v1',         // Main league listing page
    leagueDetail: 'v1',       // Individual league detail page
  },
  
  // Component-level configurations
  components: {
    leagueCard: 'v1',         // League card component
    leagueList: 'v1',         // League list component
    countryFilter: 'v1',      // Country filter component
    seasonFilter: 'v1',       // Season filter component
    leagueTable: 'v1',        // League table component
    teamList: 'v1',           // Team list component
  }
} as const;

export type LeagueFeatureConfig = typeof leagueFeatureConfig;

// Environment-based configurations
export const LEAGUE_FEATURE_VERSIONS = {
  development: {
    ...leagueFeatureConfig,
  },
  production: {
    ...leagueFeatureConfig,
  }
} as const;

// Get current configuration based on environment
export const getCurrentLeagueConfig = () => {
  const env = process.env.NODE_ENV as keyof typeof LEAGUE_FEATURE_VERSIONS;
  return LEAGUE_FEATURE_VERSIONS[env] || LEAGUE_FEATURE_VERSIONS.production;
};

// Version metadata for documentation
export const LEAGUE_VERSION_INFO = {
  leaguePage: {
    v1: { name: 'League Page V1', status: 'development', releaseDate: '2024-01-25' },
  },
  leagueDetail: {
    v1: { name: 'League Detail V1', status: 'development', releaseDate: '2024-01-25' },
  }
} as const;
