// News API Utilities - Shared utilities for API handling
// This module provides consistent API handling across news endpoints

import { NextResponse } from 'next/server';

export interface APIError {
  error: string;
  message: string;
  code: string;
  statusCode?: number;
}

/**
 * Standard error response creator
 * @param error - Error object or message
 * @param statusCode - HTTP status code
 * @returns NextResponse with error
 */
export const createErrorResponse = (
  error: unknown,
  statusCode: number = 500
): NextResponse => {
  let errorData: APIError;

  if (error instanceof Error) {
    if (error.name === 'AbortError') {
      errorData = {
        error: 'Request timeout',
        message: 'The service is taking too long to respond',
        code: 'TIMEOUT_ERROR'
      };
      statusCode = 504;
    } else if (error.message.includes('ECONNREFUSED')) {
      errorData = {
        error: 'Service unavailable',
        message: 'The service is currently unavailable',
        code: 'SERVICE_UNAVAILABLE'
      };
      statusCode = 503;
    } else {
      errorData = {
        error: 'Internal server error',
        message: error.message,
        code: 'INTERNAL_ERROR'
      };
    }
  } else if (typeof error === 'string') {
    errorData = {
      error: 'Internal server error',
      message: error,
      code: 'INTERNAL_ERROR'
    };
  } else {
    errorData = {
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR'
    };
  }

  return NextResponse.json(errorData, { status: statusCode });
};

/**
 * Try multiple backend endpoints with fallback
 * @param endpoints - Array of endpoint URLs to try
 * @param options - Fetch options
 * @returns Promise with successful response or throws error
 */
export const tryMultipleEndpoints = async (
  endpoints: string[],
  options: RequestInit = {}
): Promise<Response> => {
  let lastError: unknown;

  for (const endpoint of endpoints) {
    try {
      console.log(`🔌 Trying backend API: ${endpoint}`);

      const response = await fetch(endpoint, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Sports-Frontend-Proxy/1.0',
          ...options.headers
        },
        signal: AbortSignal.timeout(10000), // 10 seconds timeout
      });

      if (response.ok) {
        console.log(`✅ Backend API successful: ${endpoint}`);
        return response;
      } else {
        console.log(`⚠️ Endpoint failed with status ${response.status}: ${endpoint}`);
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.log(`⚠️ Endpoint failed: ${endpoint}`, error);
      lastError = error;
      continue;
    }
  }

  throw lastError || new Error('All endpoints failed');
};

/**
 * Get base URL from environment
 * @returns Base URL for backend API
 */
export const getBaseUrl = (): string => {
  return process.env.API_BASE_URL || 'http://localhost:3000';
};

/**
 * Create standard cache headers
 * @param maxAge - Cache max age in seconds
 * @param staleWhileRevalidate - Stale while revalidate time in seconds
 * @returns Headers object
 */
export const createCacheHeaders = (
  maxAge: number = 60,
  staleWhileRevalidate: number = 300
): Record<string, string> => {
  return {
    'Cache-Control': `public, s-maxage=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`,
    'Content-Type': 'application/json',
  };
};

/**
 * Create success response with standard headers
 * @param data - Response data
 * @param cacheMaxAge - Cache max age in seconds
 * @returns NextResponse with data and headers
 */
export const createSuccessResponse = (
  data: any,
  cacheMaxAge: number = 60
): NextResponse => {
  return NextResponse.json(data, {
    status: 200,
    headers: createCacheHeaders(cacheMaxAge)
  });
};

/**
 * Validate and parse query parameters
 * @param searchParams - URL search parameters
 * @returns Parsed parameters object
 */
export const parseQueryParams = (searchParams: URLSearchParams) => {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10')));
  const category = searchParams.get('category') || '';
  const search = searchParams.get('search') || '';

  return { page, limit, category, search };
};

/**
 * Build query string from parameters
 * @param params - Parameters object
 * @returns Query string
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.set(key, value.toString());
    }
  });

  return queryParams.toString();
};

/**
 * Health check for backend endpoint
 * @param endpoint - Endpoint URL to check
 * @returns Promise<boolean> indicating if endpoint is healthy
 */
export const checkEndpointHealth = async (endpoint: string): Promise<boolean> => {
  try {
    const response = await fetch(endpoint, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000),
    });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Log API request for debugging
 * @param method - HTTP method
 * @param endpoint - Endpoint URL
 * @param params - Request parameters
 */
export const logAPIRequest = (
  method: string,
  endpoint: string,
  params?: Record<string, any>
): void => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔌 ${method} ${endpoint}`, params ? { params } : '');
  }
};

/**
 * Transform backend response to frontend format
 * @param data - Backend response data
 * @param transformer - Optional transformer function
 * @returns Transformed data
 */
export const transformResponse = <T, R = T>(
  data: T,
  transformer?: (data: T) => R
): R => {
  if (transformer) {
    return transformer(data);
  }
  return data as unknown as R;
};
