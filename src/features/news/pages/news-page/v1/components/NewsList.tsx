'use client';

import React from 'react';
import { NewsCard } from './NewsCard';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';
import { EmptyState } from './EmptyState';
import type { NewsArticle } from '@/lib/api/news';

interface NewsListProps {
  articles: NewsArticle[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  onLoadMore: () => void;
  selectedCategory: string;
  searchQuery: string;
  variant?: 'list' | 'grid';
}

/**
 * News List Component
 * 
 * Displays a vertical list of news articles with loading states,
 * error handling, and infinite scroll functionality.
 */
export const NewsList: React.FC<NewsListProps> = ({
  articles,
  isLoading,
  error,
  hasMore,
  onLoadMore,
  selectedCategory,
  searchQuery,
  variant = 'list'
}) => {
  // Show loading state for initial load
  if (isLoading && articles.length === 0) {
    return <LoadingState />;
  }

  // Show error state
  if (error && articles.length === 0) {
    return <ErrorState error={error} onRetry={onLoadMore} />;
  }

  // Show empty state
  if (!isLoading && articles.length === 0) {
    return (
      <EmptyState
        selectedCategory={selectedCategory}
        searchQuery={searchQuery}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {searchQuery ? (
              <>Search results for "{searchQuery}"</>
            ) : selectedCategory === 'all' ? (
              'Latest News'
            ) : (
              `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} News`
            )}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            {articles.length} article{articles.length !== 1 ? 's' : ''} found
          </p>
        </div>
      </div>

      {/* News Articles */}
      <div className={variant === 'grid'
        ? "grid grid-cols-1 md:grid-cols-2 gap-6"
        : "space-y-4"
      }>
        {articles.map((article, index) => (
          <NewsCard
            key={`${article.id}-${index}`}
            article={article}
            variant={variant === 'grid' ? 'compact' : (index === 0 && !searchQuery ? 'featured' : 'default')}
          />
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="flex justify-center pt-8">
          <button
            onClick={onLoadMore}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Loading...</span>
              </>
            ) : (
              <span>Load More Articles</span>
            )}
          </button>
        </div>
      )}

      {/* Loading More Indicator */}
      {isLoading && articles.length > 0 && (
        <div className="flex justify-center py-4">
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
            <span>Loading more articles...</span>
          </div>
        </div>
      )}

      {/* End of Results */}
      {!hasMore && articles.length > 0 && (
        <div className="text-center py-8 border-t border-gray-200">
          <p className="text-gray-500">You've reached the end of the articles</p>
        </div>
      )}
    </div>
  );
};
