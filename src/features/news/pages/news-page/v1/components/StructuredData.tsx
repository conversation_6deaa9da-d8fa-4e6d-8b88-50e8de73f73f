'use client';

import React from 'react';
import type { NewsArticle } from '@/lib/api/news';

interface StructuredDataProps {
  articles: NewsArticle[];
  currentPage?: number;
  totalPages?: number;
}

/**
 * Structured Data Component for News Page SEO
 * 
 * Generates JSON-LD structured data for better search engine understanding
 * and rich snippets in search results.
 */
export const StructuredData: React.FC<StructuredDataProps> = ({
  articles,
  currentPage = 1,
  totalPages = 1
}) => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

  // Website structured data
  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Sports News Platform",
    "url": baseUrl,
    "description": "Your ultimate source for sports news, breaking football stories, transfer updates, and match reports.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/news?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Sports News Platform",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/images/logo.png`,
        "width": 200,
        "height": 60
      }
    }
  };

  // News collection structured data
  const newsCollectionData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Latest Sports News",
    "description": "Stay updated with the latest sports news, breaking football stories, transfer updates, match reports, and exclusive interviews.",
    "url": `${baseUrl}/news`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": articles.length,
      "itemListElement": articles.map((article, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "NewsArticle",
          "@id": `${baseUrl}/news/${article.slug}`,
          "headline": article.title,
          "description": article.excerpt,
          "image": article.featuredImage 
            ? `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE}${article.featuredImage}`
            : `${baseUrl}/images/og/news-default.jpg`,
          "datePublished": article.publishedAt,
          "dateModified": article.updatedAt || article.publishedAt,
          "author": {
            "@type": "Person",
            "name": "Sports News Team"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Sports News Platform",
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            }
          },
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `${baseUrl}/news/${article.slug}`
          },
          "articleSection": article.category?.name || "Sports News",
          "keywords": article.tags?.join(", ") || "sports, news, football",
          "url": `${baseUrl}/news/${article.slug}`
        }
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": baseUrl
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "News",
          "item": `${baseUrl}/news`
        }
      ]
    }
  };

  // Pagination structured data (if applicable)
  const paginationData = totalPages > 1 ? {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "url": `${baseUrl}/news?page=${currentPage}`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": articles.length
    },
    ...(currentPage > 1 && {
      "relatedLink": [
        {
          "@type": "WebPage",
          "url": `${baseUrl}/news?page=${currentPage - 1}`,
          "name": "Previous Page"
        }
      ]
    }),
    ...(currentPage < totalPages && {
      "relatedLink": [
        {
          "@type": "WebPage", 
          "url": `${baseUrl}/news?page=${currentPage + 1}`,
          "name": "Next Page"
        }
      ]
    })
  } : null;

  // Organization structured data
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Sports News Platform",
    "url": baseUrl,
    "logo": `${baseUrl}/images/logo.png`,
    "description": "Leading sports news platform providing comprehensive coverage of football, transfers, matches, and exclusive interviews.",
    "sameAs": [
      "https://twitter.com/sportsnews",
      "https://facebook.com/sportsnews",
      "https://instagram.com/sportsnews"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    }
  };

  return (
    <>
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData)
        }}
      />

      {/* News Collection Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(newsCollectionData)
        }}
      />

      {/* Organization Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData)
        }}
      />

      {/* Pagination Structured Data */}
      {paginationData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(paginationData)
          }}
        />
      )}
    </>
  );
};
