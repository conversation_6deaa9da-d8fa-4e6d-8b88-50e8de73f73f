# 🔌 API Documentation & Integration Guide

## 📋 Overview
Complete documentation for all API endpoints, data flows, and integration patterns in the Sports News Platform.

---

## 🏗️ API Architecture

### Proxy Layer Strategy
```
Frontend → Next.js API Routes → Backend API → Database
```

**Benefits**:
- **Security**: Hide backend URLs and API keys
- **Caching**: Next.js built-in caching and revalidation
- **Error Handling**: Centralized error processing
- **CORS**: Simplified cross-origin requests
- **Fallback**: Mock data for development

---

## ⚽ Football APIs

### GET /api/football/fixtures
**Purpose**: Fetch football fixtures with filtering and pagination

#### Request Parameters
```typescript
interface FixturesParams {
  page?: number;        // Page number (default: 1)
  limit?: number;       // Items per page (default: 100)
  league?: string;      // League name for client-side filtering
  leagueId?: number;    // League ID for backend filtering
  date?: string;        // Date filter (YYYY-MM-DD format)
}
```

#### Backend Endpoints (Fallback Order)
1. `http://localhost:3000/football/fixtures` (with query params)
2. `http://localhost:3000/football/fixtures/upcoming-and-live` (default)

#### Response Format
```typescript
interface FixturesResponse {
  data: Fixture[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

interface Fixture {
  id: number;
  externalId: string;
  homeTeam: {
    id: number;
    name: string;
    logo: string;
  };
  awayTeam: {
    id: number;
    name: string;
    logo: string;
  };
  league: {
    id: number;
    name: string;
    logo: string;
    season: string;
  };
  matchDateTime: string;    // ISO 8601 format
  status: string;          // 'NS', 'LIVE', 'FT', etc.
  minute: number | null;   // Current minute if live
  homeScore: number | null;
  awayScore: number | null;
  isHot: boolean;         // Featured/hot match indicator
  venue: string;
  referee: string;
}
```

#### Caching Strategy
- **Cache Duration**: 30 seconds
- **Stale While Revalidate**: 300 seconds
- **Error Handling**: 504 for timeout, 500 for server errors

#### Usage Examples
```typescript
// Fetch all fixtures
const fixtures = await fetch('/api/football/fixtures?page=1&limit=50');

// Filter by league
const premierLeague = await fetch('/api/football/fixtures?leagueId=850');

// Filter by date
const todayFixtures = await fetch('/api/football/fixtures?date=2024-01-25');
```

### GET /api/football/leagues
**Purpose**: Fetch football leagues with optional filtering

#### Request Parameters
```typescript
interface LeaguesParams {
  active?: boolean;     // Filter active leagues only (default: true)
}
```

#### Backend Endpoint
- `http://localhost:3000/football/leagues`

#### Response Format
```typescript
interface LeaguesResponse {
  data: League[];
  status: number;
}

interface League {
  id: number;
  externalId: string;
  name: string;
  logo: string;
  country: string;
  season: string;
  isActive: boolean;
  priority: number;
  fixtureCount: number;
}
```

#### Caching Strategy
- **Cache Duration**: 5 minutes
- **Stale While Revalidate**: 30 minutes

---

## 📰 News APIs

### GET /api/news
**Purpose**: Fetch news articles with pagination, filtering, and search

#### Request Parameters
```typescript
interface NewsParams {
  page?: number;        // Page number (default: 1)
  limit?: number;       // Items per page (default: 10)
  category?: string;    // Category slug filter
  search?: string;      // Search query
  featured?: boolean;   // Featured articles only
  published?: boolean;  // Published articles only (default: true)
}
```

#### Backend Endpoints (Fallback Order)
1. `http://localhost:3000/news/articles`
2. `http://localhost:3000/news/published`
3. `http://localhost:3000/news`
4. `http://localhost:3000/public/news`

#### Response Format
```typescript
interface NewsResponse {
  data: Article[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

interface Article {
  id: number;
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  publishedAt: string;
  updatedAt: string;
  readTime: number;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isPublished: boolean;
  isFeatured: boolean;
  category: {
    id: number;
    slug: string;
    name: string;
    color: string;
  };
  author: {
    id: number;
    name: string;
    avatar: string;
    bio: string;
  };
  tags: string[];
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
}
```

#### Fallback Strategy
- **Mock Data**: 5 sample articles for development
- **Caching**: 60 seconds for real data, 30 seconds for mock data

### GET /api/news/categories
**Purpose**: Fetch news categories

#### Backend Endpoint
- `http://localhost:3000/news/categories`

#### Response Format
```typescript
interface CategoriesResponse {
  data: Category[];
  status: number;
}

interface Category {
  id: number;
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  sortOrder: number;
  isActive: boolean;
  isPublic: boolean;
  articleCount: number;
  publishedArticleCount: number;
  createdAt: string;
  updatedAt: string;
}
```

#### Status
✅ **Working with real backend data**

### GET /api/news/article/[slug]
**Purpose**: Fetch individual news article by slug

#### Backend Endpoints (Fallback Order)
1. `http://localhost:3000/news/article/{slug}`
2. `http://localhost:3000/news/articles/{slug}`
3. `http://localhost:3000/public/news/article/{slug}`
4. `http://localhost:3000/public/news/articles/{slug}`

#### Response Format
```typescript
interface ArticleResponse {
  success: boolean;
  article: Article | null;
  message?: string;
}
```

#### Fallback Strategy
- **Mock Data**: Development articles for testing
- **Caching**: 5 minutes with Next.js revalidation
- **SEO**: Automatic metadata generation

### GET /api/news/related/[slug]
**Purpose**: Fetch related articles for a specific article

#### Backend Endpoints (Fallback Order)
1. `http://localhost:3000/news/related/{slug}?limit=4`
2. `http://localhost:3000/news/articles/related/{slug}?limit=4`
3. `http://localhost:3000/public/news/related/{slug}?limit=4`
4. `http://localhost:3000/news/similar/{slug}?limit=4`

#### Response Format
```typescript
interface RelatedArticlesResponse {
  success: boolean;
  articles: RelatedArticle[];
  message?: string;
}

interface RelatedArticle {
  id: number;
  slug: string;
  title: string;
  excerpt: string;
  featuredImage: string;
  publishedAt: string;
  readTime: number;
  category: {
    id: number;
    slug: string;
    name: string;
  };
}
```

#### Fallback Strategy
- **Mock Data**: Related articles for development
- **Caching**: 10 minutes with Next.js revalidation

---

## 🔧 API Client Libraries

### Football API Client
**Location**: `src/lib/api/fixtures.ts`

```typescript
// Fetch fixtures with filtering
export async function fetchFixtures(params: FixturesParams): Promise<FixturesResponse> {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.leagueId) searchParams.set('leagueId', params.leagueId.toString());
  if (params.date) searchParams.set('date', params.date);

  const response = await fetch(`/api/football/fixtures?${searchParams}`);
  return response.json();
}

// Fetch leagues
export async function fetchLeagues(active = true): Promise<LeaguesResponse> {
  const response = await fetch(`/api/football/leagues?active=${active}`);
  return response.json();
}
```

### News API Client
**Location**: `src/lib/api/news.ts`

```typescript
// Fetch news articles
export async function fetchNews(params: NewsParams): Promise<NewsResponse> {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      searchParams.set(key, value.toString());
    }
  });

  const response = await fetch(`/api/news?${searchParams}`);
  return response.json();
}

// Fetch news categories
export async function fetchNewsCategories(): Promise<CategoriesResponse> {
  const response = await fetch('/api/news/categories');
  return response.json();
}

// Fetch article by slug
export async function fetchArticleBySlug(slug: string): Promise<ArticleResponse> {
  const response = await fetch(`/api/news/article/${slug}`);
  return response.json();
}

// Fetch related articles
export async function fetchRelatedArticles(slug: string): Promise<RelatedArticlesResponse> {
  const response = await fetch(`/api/news/related/${slug}`);
  return response.json();
}
```

---

## 🔄 Error Handling Patterns

### API Route Error Handling
```typescript
export async function GET(request: NextRequest) {
  try {
    // Try multiple backend endpoints
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          headers: { 'Content-Type': 'application/json' },
          next: { revalidate: 300 }
        });

        if (response.ok) {
          const data = await response.json();
          return NextResponse.json({ success: true, data });
        }
      } catch (endpointError) {
        console.log(`Endpoint ${endpoint} failed:`, endpointError);
        continue;
      }
    }

    // Fallback to mock data
    return NextResponse.json({ 
      success: true, 
      data: mockData,
      source: 'mock' 
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Client-Side Error Handling
```typescript
export async function fetchWithErrorHandling<T>(
  url: string,
  options?: RequestInit
): Promise<T> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}
```

---

## 📊 Performance Optimization

### Caching Strategy
```typescript
// API Route Caching
export async function GET() {
  return NextResponse.json(data, {
    headers: {
      'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300'
    }
  });
}

// Next.js Revalidation
const response = await fetch(endpoint, {
  next: { revalidate: 300 } // 5 minutes
});
```

### Request Optimization
- **Parallel Requests**: Use `Promise.all()` for independent requests
- **Request Deduplication**: Next.js automatic deduplication
- **Pagination**: Limit data per request
- **Selective Fields**: Request only needed data

---

## 🔒 Security Measures

### API Security
- **Proxy Layer**: Hide backend URLs
- **Environment Variables**: Secure configuration
- **CORS Headers**: Proper cross-origin setup
- **Rate Limiting**: Timeout controls
- **Input Validation**: Sanitize all inputs

### Data Validation
```typescript
// Runtime type checking
function validateFixtureResponse(data: any): data is FixturesResponse {
  return (
    data &&
    Array.isArray(data.data) &&
    typeof data.meta === 'object' &&
    typeof data.status === 'number'
  );
}
```

---

## 🚀 Development Guidelines

### Adding New APIs
1. **Create API Route**: `app/api/[endpoint]/route.ts`
2. **Implement Fallback**: Multiple endpoint strategy
3. **Add Client Library**: `lib/api/[service].ts`
4. **Handle Errors**: Graceful fallback
5. **Add Caching**: Performance optimization
6. **Document**: Update this documentation

### Testing APIs
```bash
# Test endpoints directly
curl http://localhost:5000/api/football/fixtures
curl http://localhost:5000/api/news/categories

# Test with parameters
curl "http://localhost:5000/api/news?page=1&limit=5&category=transfer-news"
```

---

*This API documentation serves as the complete reference for all API integrations and data flows.*
