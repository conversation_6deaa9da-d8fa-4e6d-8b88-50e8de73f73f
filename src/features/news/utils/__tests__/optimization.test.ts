// News Optimization Tests
// Tests to verify the optimization improvements work correctly

import { describe, it, expect } from '@jest/globals';
import { 
  getImageUrl, 
  handleImageError, 
  optimizeImageUrl 
} from '../imageUtils';
import { 
  parseNewsError, 
  getUserFriendlyErrorMessage, 
  isRetryableError 
} from '../errorUtils';
import { 
  mergeCategories, 
  getCategoryIcon, 
  getCategoryColors,
  formatCategoryDisplayName 
} from '../categoryUtils';
import { 
  parseQueryParams, 
  buildQueryString 
} from '../apiUtils';

describe('Image Utils Optimization', () => {
  it('should generate correct image URLs', () => {
    // Test with null image
    expect(getImageUrl(null)).toContain('placeholder.com');
    
    // Test with full URL
    expect(getImageUrl('https://example.com/image.jpg')).toBe('https://example.com/image.jpg');
    
    // Test with relative path
    process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE = 'https://cdn.example.com';
    expect(getImageUrl('/images/test.jpg')).toBe('https://cdn.example.com/images/test.jpg');
  });

  it('should optimize image URLs correctly', () => {
    const originalUrl = 'https://cdn.example.com/image.jpg';
    const optimized = optimizeImageUrl(originalUrl, 400, 80);
    
    expect(optimized).toContain('w=400');
    expect(optimized).toContain('q=80');
  });

  it('should handle image errors correctly', () => {
    const mockEvent = {
      target: { src: '' }
    } as any;

    handleImageError(mockEvent, 'Test+Image');
    expect(mockEvent.target.src).toContain('placeholder.com');
    expect(mockEvent.target.src).toContain('Test+Image');
  });
});

describe('Error Utils Optimization', () => {
  it('should parse different error types correctly', () => {
    // Test Error instance
    const error = new Error('Test error');
    const parsed = parseNewsError(error);
    expect(parsed.message).toBe('Test error');
    expect(parsed.code).toBe('CLIENT_ERROR');

    // Test string error
    const stringError = parseNewsError('String error');
    expect(stringError.message).toBe('String error');
    expect(stringError.code).toBe('UNKNOWN_ERROR');

    // Test API error object
    const apiError = { message: 'API error', code: 'API_ERROR', status: 500 };
    const parsedApi = parseNewsError(apiError);
    expect(parsedApi.message).toBe('API error');
    expect(parsedApi.code).toBe('API_ERROR');
  });

  it('should provide user-friendly error messages', () => {
    const timeoutError = { message: 'Timeout', code: 'TIMEOUT_ERROR' };
    expect(getUserFriendlyErrorMessage(timeoutError)).toContain('taking too long');

    const networkError = { message: 'Network', code: 'NETWORK_ERROR' };
    expect(getUserFriendlyErrorMessage(networkError)).toContain('connection error');
  });

  it('should identify retryable errors correctly', () => {
    expect(isRetryableError({ message: 'Timeout', code: 'TIMEOUT_ERROR' })).toBe(true);
    expect(isRetryableError({ message: 'Not found', code: 'NOT_FOUND' })).toBe(false);
    expect(isRetryableError({ message: 'Network', code: 'NETWORK_ERROR' })).toBe(true);
  });
});

describe('Category Utils Optimization', () => {
  it('should merge categories correctly', () => {
    const apiCategories = [
      {
        id: '1',
        name: 'Football',
        slug: 'football',
        icon: '⚽',
        color: '#3B82F6',
        count: 10,
        sortOrder: 1,
        description: 'Football news',
        isActive: true,
        isPublic: true,
        metaTitle: null,
        metaDescription: null,
        articleCount: 10,
        publishedArticleCount: 10,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }
    ];

    const merged = mergeCategories(apiCategories);
    
    // Should include "All News" as first item
    expect(merged[0].slug).toBe('all');
    expect(merged[0].name).toBe('All News');
    
    // Should include API category
    expect(merged[1].name).toBe('Football');
    expect(merged[1].slug).toBe('football');
  });

  it('should get correct category icons', () => {
    expect(getCategoryIcon('match')).toBe('⚽');
    expect(getCategoryIcon('breaking')).toBe('🔥');
    expect(getCategoryIcon('unknown')).toBe('📰'); // fallback
  });

  it('should get correct category colors', () => {
    expect(getCategoryColors('breaking')).toContain('red');
    expect(getCategoryColors('match')).toContain('blue');
    expect(getCategoryColors('unknown')).toContain('gray'); // fallback
  });

  it('should format category display names correctly', () => {
    const category = {
      id: '1',
      name: 'Football',
      slug: 'football',
      icon: '⚽',
      color: '#3B82F6',
      count: 10
    };

    expect(formatCategoryDisplayName(category, false)).toBe('Football');
    expect(formatCategoryDisplayName(category, true)).toBe('Football (10)');
  });
});

describe('API Utils Optimization', () => {
  it('should parse query parameters correctly', () => {
    const searchParams = new URLSearchParams('page=2&limit=20&category=football&search=test');
    const parsed = parseQueryParams(searchParams);

    expect(parsed.page).toBe(2);
    expect(parsed.limit).toBe(20);
    expect(parsed.category).toBe('football');
    expect(parsed.search).toBe('test');
  });

  it('should handle invalid query parameters', () => {
    const searchParams = new URLSearchParams('page=invalid&limit=999');
    const parsed = parseQueryParams(searchParams);

    expect(parsed.page).toBe(1); // fallback to 1
    expect(parsed.limit).toBe(100); // max limit
  });

  it('should build query strings correctly', () => {
    const params = {
      page: 2,
      limit: 20,
      category: 'football',
      search: 'test',
      empty: '',
      undefined: undefined,
      null: null
    };

    const queryString = buildQueryString(params);
    
    expect(queryString).toContain('page=2');
    expect(queryString).toContain('limit=20');
    expect(queryString).toContain('category=football');
    expect(queryString).toContain('search=test');
    expect(queryString).not.toContain('empty=');
    expect(queryString).not.toContain('undefined=');
    expect(queryString).not.toContain('null=');
  });
});

describe('Integration Tests', () => {
  it('should work together seamlessly', () => {
    // Test that utilities work together
    const imageUrl = getImageUrl('/test.jpg');
    const categoryIcon = getCategoryIcon('match');
    const error = parseNewsError(new Error('Test'));
    const queryString = buildQueryString({ page: 1, limit: 10 });

    expect(imageUrl).toBeDefined();
    expect(categoryIcon).toBeDefined();
    expect(error).toBeDefined();
    expect(queryString).toBeDefined();
  });
});
