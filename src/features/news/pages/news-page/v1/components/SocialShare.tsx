'use client';

import React, { useState } from 'react';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  variant?: 'horizontal' | 'vertical' | 'dropdown';
}

/**
 * Social Share Component
 * 
 * Provides social media sharing functionality for news articles.
 * Supports multiple platforms and sharing methods.
 */
export const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description = '',
  className = '',
  variant = 'horizontal'
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);

  const shareLinks = [
    {
      name: 'Facebook',
      icon: '📘',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      color: 'hover:bg-blue-50 hover:text-blue-600'
    },
    {
      name: 'Twitter',
      icon: '🐦',
      url: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
      color: 'hover:bg-sky-50 hover:text-sky-600'
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      color: 'hover:bg-blue-50 hover:text-blue-700'
    },
    {
      name: 'WhatsApp',
      icon: '💬',
      url: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
      color: 'hover:bg-green-50 hover:text-green-600'
    },
    {
      name: 'Telegram',
      icon: '✈️',
      url: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
      color: 'hover:bg-blue-50 hover:text-blue-500'
    }
  ];

  const handleShare = (shareUrl: string, platform: string) => {
    // Track sharing analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'share', {
        method: platform.toLowerCase(),
        content_type: 'article',
        item_id: url
      });
    }

    // Open share window
    window.open(
      shareUrl,
      'share-dialog',
      'width=600,height=400,resizable=yes,scrollbars=yes'
    );
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      
      // Track copy analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'share', {
          method: 'copy_link',
          content_type: 'article',
          item_id: url
        });
      }

      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url
        });
        
        // Track native share
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'share', {
            method: 'native',
            content_type: 'article',
            item_id: url
          });
        }
      } catch (err) {
        console.error('Error sharing:', err);
      }
    }
  };

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors duration-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          <span>Share</span>
        </button>

        {isDropdownOpen && (
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
            <div className="py-2">
              {shareLinks.map((link) => (
                <button
                  key={link.name}
                  onClick={() => {
                    handleShare(link.url, link.name);
                    setIsDropdownOpen(false);
                  }}
                  className={`w-full flex items-center space-x-3 px-4 py-2 text-left text-gray-700 ${link.color} transition-colors duration-200`}
                >
                  <span className="text-lg">{link.icon}</span>
                  <span>{link.name}</span>
                </button>
              ))}
              
              <hr className="my-2" />
              
              <button
                onClick={() => {
                  copyToClipboard();
                  setIsDropdownOpen(false);
                }}
                className="w-full flex items-center space-x-3 px-4 py-2 text-left text-gray-700 hover:bg-gray-50 transition-colors duration-200"
              >
                <span className="text-lg">🔗</span>
                <span>{copySuccess ? 'Copied!' : 'Copy Link'}</span>
              </button>

              {navigator.share && (
                <button
                  onClick={() => {
                    handleNativeShare();
                    setIsDropdownOpen(false);
                  }}
                  className="w-full flex items-center space-x-3 px-4 py-2 text-left text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  <span className="text-lg">📱</span>
                  <span>More Options</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  const containerClass = variant === 'vertical' 
    ? 'flex flex-col space-y-2' 
    : 'flex items-center space-x-2';

  return (
    <div className={`${containerClass} ${className}`}>
      {shareLinks.slice(0, 4).map((link) => (
        <button
          key={link.name}
          onClick={() => handleShare(link.url, link.name)}
          className={`flex items-center justify-center w-8 h-8 rounded-full text-gray-600 ${link.color} transition-colors duration-200`}
          title={`Share on ${link.name}`}
        >
          <span className="text-lg">{link.icon}</span>
        </button>
      ))}
      
      <button
        onClick={copyToClipboard}
        className="flex items-center justify-center w-8 h-8 rounded-full text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200"
        title={copySuccess ? 'Copied!' : 'Copy Link'}
      >
        <span className="text-lg">{copySuccess ? '✅' : '🔗'}</span>
      </button>

      {navigator.share && (
        <button
          onClick={handleNativeShare}
          className="flex items-center justify-center w-8 h-8 rounded-full text-gray-600 hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200"
          title="Share"
        >
          <span className="text-lg">📱</span>
        </button>
      )}
    </div>
  );
};
