import { Metadata, ResolvingMetadata } from 'next';
import { notFound } from 'next/navigation';
import { NewsDetailPage } from '@/features/news/pages';

/**
 * News Detail Route - Dynamic Page with SEO Optimization
 * 
 * This page serves individual news articles with dynamic metadata
 * for optimal SEO performance and social media sharing.
 */

interface Props {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

// Function to fetch article data directly from backend API
async function getArticle(slug: string) {
  try {
    const backendUrl = process.env.API_BASE_URL || process.env.BASE_URL || 'http://localhost:3000';
    console.log(`🔌 Fetching article directly from backend: ${backendUrl}`);

    // Try multiple backend endpoints for article detail
    const endpoints = [
      `${backendUrl}/news/${slug}`,           // Primary endpoint: news/{slug}
      `${backendUrl}/news/article/${slug}`,   // Alternative: news/article/{slug}
      `${backendUrl}/news/articles/${slug}`,  // Alternative: news/articles/{slug}
      `${backendUrl}/public/news/${slug}`,    // Public endpoint: public/news/{slug}
      `${backendUrl}/public/news/article/${slug}`, // Public alternative
      `${backendUrl}/public/news/articles/${slug}` // Public alternative
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔌 Trying backend endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          headers: {
            'Content-Type': 'application/json',
          },
          next: { revalidate: 300 }, // Revalidate every 5 minutes
          signal: AbortSignal.timeout(10000) // 10 seconds timeout
        });

        console.log(`📡 Response status: ${response.status} for ${endpoint}`);

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Backend API successful for article');

          // Handle different response formats
          let article = null;
          if (data.article) {
            article = data.article;
          } else if (data.data) {
            article = data.data;
          } else if (data.id) {
            article = data;
          }

          if (article) {
            console.log('✅ Article found:', article.title || article.slug);
            return article;
          }
        } else {
          console.log(`⚠️ Backend returned ${response.status}: ${response.statusText}`);
        }
      } catch (endpointError) {
        console.log(`⚠️ Endpoint ${endpoint} failed:`, endpointError);
        continue;
      }
    }

    console.log(`❌ All backend endpoints failed for article: ${slug}`);
    return null;
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

// Dynamic metadata generation for SEO optimization
export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const slug = (await params).slug;
  const article = await getArticle(slug);

  if (!article) {
    return {
      title: 'Article Not Found | Sports News',
      description: 'The requested article could not be found.',
    };
  }

  // Get parent metadata for inheritance
  const previousImages = (await parent).openGraph?.images || [];

  // Build image URL
  const imageUrl = article.featuredImage
    ? `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || ''}${article.featuredImage}`
    : '/images/og/news-default.jpg';

  return {
    title: `${article.title} | Sports News`,
    description: article.excerpt,
    keywords: [
      ...(article.tags || []),
      article.category.name.toLowerCase(),
      'sports news',
      'football news',
      'latest news'
    ],
    authors: [{ name: article.author?.fullName || article.author?.username || 'Sports News Team' }],
    creator: article.author?.fullName || article.author?.username || 'Sports News Team',
    publisher: 'Sports News Platform',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'),
    alternates: {
      canonical: `/news/${article.slug}`,
    },
    openGraph: {
      title: article.title,
      description: article.excerpt,
      url: `/news/${article.slug}`,
      siteName: 'Sports News Platform',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: article.title,
        },
        ...previousImages,
      ],
      locale: 'en_US',
      type: 'article',
      publishedTime: article.publishedAt,
      modifiedTime: article.updatedAt,
      authors: [article.author?.fullName || article.author?.username || 'Sports News Team'],
      section: article.category.name,
      tags: article.tags || [],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.excerpt,
      images: [imageUrl],
      creator: '@sportsnews',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    other: {
      'article:author': article.author?.fullName || article.author?.username || 'Sports News Team',
      'article:published_time': article.publishedAt,
      'article:modified_time': article.updatedAt,
      'article:section': article.category.name,
      'article:tag': (article.tags || []).join(','),
    },
  };
}

// Generate static params for static generation (optional)
export async function generateStaticParams() {
  // This would fetch all article slugs from your API
  // For now, return empty array to use dynamic rendering
  return [];
}

export default async function NewsDetailRoute({ params }: Props) {
  const slug = (await params).slug;
  const article = await getArticle(slug);

  if (!article) {
    notFound();
  }

  return <NewsDetailPage article={article} />;
}
