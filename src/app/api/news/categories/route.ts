import { NextRequest, NextResponse } from 'next/server';

// News Categories API Proxy - Secure proxy for news categories endpoint
// This proxy connects to the real backend API for news categories

const BASE_URL = process.env.BASE_URL || process.env.API_BASE_URL || 'http://localhost:3000';

interface NewsCategory {
  id: number;
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  sortOrder: number;
  isActive: boolean;
  isPublic: boolean;
  metaTitle: string | null;
  metaDescription: string | null;
  articleCount: number;
  publishedArticleCount: number;
  createdAt: string;
  updatedAt: string;
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔌 Fetching news categories from backend API');

    // Construct backend API URL
    const backendUrl = `${BASE_URL}/news/categories`;
    console.log('🔌 Backend categories URL:', backendUrl);

    // Make request to backend API
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sports-Frontend-Proxy/1.0',
      },
      // Add timeout
      signal: AbortSignal.timeout(10000), // 10 seconds timeout
    });

    if (!response.ok) {
      console.error('❌ Backend categories API error:', response.status, response.statusText);
      throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
    }

    const categories: NewsCategory[] = await response.json();

    console.log('✅ News categories API successful, categories count:', categories.length);

    // Filter only public and active categories
    const publicCategories = categories.filter(cat => cat.isActive && cat.isPublic);

    // Transform to frontend format
    const transformedCategories = publicCategories.map(cat => ({
      id: cat.id.toString(),
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      icon: mapIconToEmoji(cat.icon),
      color: cat.color,
      count: cat.publishedArticleCount,
      sortOrder: cat.sortOrder
    }));

    // Return proxied response
    return NextResponse.json({
      data: transformedCategories,
      meta: {
        totalItems: transformedCategories.length,
        totalPages: 1,
        currentPage: 1,
        limit: transformedCategories.length
      },
      status: 200
    }, {
      status: 200,
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // Cache for 5 minutes
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('❌ News categories proxy error:', error);

    // Handle different types of errors
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json(
          {
            error: 'Request timeout',
            message: 'The news categories service is taking too long to respond',
            code: 'TIMEOUT_ERROR'
          },
          { status: 504 }
        );
      }

      if (error.message.includes('ECONNREFUSED')) {
        return NextResponse.json(
          {
            error: 'Service unavailable',
            message: 'The news categories service is currently unavailable',
            code: 'SERVICE_UNAVAILABLE'
          },
          { status: 503 }
        );
      }
    }

    // Generic error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to fetch news categories',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// Map backend icon names to emojis
function mapIconToEmoji(icon: string): string {
  const iconMap: Record<string, string> = {
    transfer: '🔄',
    match: '⚽',
    interview: '🎤',
    league: '🏆',
    medical: '🏥',
    tactics: '📋',
    youth: '👶',
    womens: '👩',
    breaking: '🔥',
    general: '📰'
  };
  
  return iconMap[icon] || '📰';
}

// Health check endpoint
export async function HEAD() {
  try {
    const response = await fetch(`${BASE_URL}/news/categories`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000),
    });

    return new NextResponse(null, {
      status: response.ok ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}
