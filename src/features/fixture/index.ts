// Fixture Feature - Main Exports
// This file exports all public APIs for the fixture feature

// Configuration
export { fixtureFeatureConfig, getCurrentFixtureConfig } from './config';

// Pages (will be implemented)
// export { FixturePage, FixtureDetail } from './pages';

// Components (will be implemented)
// export { FixtureCard, FixtureList, LeagueFilter } from './components';

// Types
export type { FixtureFeatureConfig } from './config';
export type * from './types';
