// News Feature Configuration
// This file manages all version configurations for the news feature

export const newsFeatureConfig = {
  // Page-level configurations
  pages: {
    newsPage: 'v2',           // Main news listing page (restructured)
    newsDetail: 'v2',         // Individual news article page (restructured)
  },

  // Component-level configurations
  components: {
    newsCard: 'v1',           // News card component
    categoryFilter: 'v1',     // Category filter component
    searchBar: 'v1',          // Search bar component
    newsGrid: 'v1',           // News grid layout
    newsHero: 'v1',           // News hero section
    pagination: 'v1',         // Pagination component
  }
} as const;

export type NewsFeatureConfig = typeof newsFeatureConfig;

// Environment-based configurations
export const NEWS_FEATURE_VERSIONS = {
  development: {
    ...newsFeatureConfig,
    pages: {
      ...newsFeatureConfig.pages,
      newsPage: 'v1',         // Latest for development
    }
  },
  production: {
    ...newsFeatureConfig,
    pages: {
      ...newsFeatureConfig.pages,
      newsPage: 'v1',         // Stable for production
    }
  }
} as const;

// Get current configuration based on environment
export const getCurrentNewsConfig = () => {
  const env = process.env.NODE_ENV as keyof typeof NEWS_FEATURE_VERSIONS;
  return NEWS_FEATURE_VERSIONS[env] || NEWS_FEATURE_VERSIONS.production;
};

// Version metadata for documentation
export const NEWS_VERSION_INFO = {
  newsPage: {
    v1: { name: 'News Page V1', status: 'development', releaseDate: '2024-01-25' },
  },
  newsDetail: {
    v1: { name: 'News Detail V1', status: 'development', releaseDate: '2024-01-25' },
  }
} as const;

// Helper function to get page version
export const getPageVersion = (pageName: keyof typeof newsFeatureConfig.pages) => {
  const config = getCurrentNewsConfig();
  return config.pages[pageName];
};

// Helper function to get component version
export const getComponentVersion = (componentName: keyof typeof newsFeatureConfig.components) => {
  const config = getCurrentNewsConfig();
  return config.components[componentName];
};
