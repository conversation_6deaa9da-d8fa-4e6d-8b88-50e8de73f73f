import { Metadata } from 'next';
import { NewsPage } from "@/features/news/pages";
import { fetchNewsCategories } from '@/lib/api/newsCategories';

interface CategoryPageProps {
  params: Promise<{ category: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * News Category Route - Next.js App Router Page
 *
 * This page serves news articles filtered by category.
 * URL Structure: /news-category/{category}?page={page}
 *
 * @see src/features/news/pages/news-page/v2/NewsPageV2.tsx
 */

// Generate metadata dynamically based on category
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  
  // Fetch categories to get category name
  let categoryName = category;
  try {
    const categoriesResponse = await fetchNewsCategories();
    const categoryData = categoriesResponse.data.find(cat => cat.slug === category);
    if (categoryData) {
      categoryName = categoryData.name;
    }
  } catch (error) {
    console.warn('Failed to fetch category data for metadata:', error);
  }

  const title = `${categoryName} News | Latest ${categoryName} Updates & Stories`;
  const description = `Stay updated with the latest ${categoryName.toLowerCase()} news, breaking stories, analysis, and exclusive coverage. Your ultimate source for ${categoryName.toLowerCase()} journalism.`;

  return {
    title,
    description,
    keywords: [
      `${categoryName.toLowerCase()} news`,
      `${categoryName.toLowerCase()} updates`,
      `${categoryName.toLowerCase()} stories`,
      'sports news',
      'breaking news',
      'latest news',
      `${categoryName.toLowerCase()} coverage`,
      `${categoryName.toLowerCase()} analysis`
    ],
    authors: [{ name: 'Sports News Team' }],
    creator: 'Sports News Platform',
    publisher: 'Sports News Platform',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'),
    alternates: {
      canonical: `/news-category/${category}`,
    },
    openGraph: {
      title,
      description,
      url: `/news-category/${category}`,
      siteName: 'Sports News Platform',
      images: [
        {
          url: `/images/og/news-category-${category}.jpg`,
          width: 1200,
          height: 630,
          alt: `${categoryName} News`,
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`/images/og/news-category-${category}.jpg`],
      creator: '@sportsnews',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

// Generate static params for known categories (optional, for better performance)
export async function generateStaticParams() {
  try {
    const categoriesResponse = await fetchNewsCategories();
    return categoriesResponse.data.map((category) => ({
      category: category.slug,
    }));
  } catch (error) {
    console.warn('Failed to generate static params for categories:', error);
    // Return common categories as fallback
    return [
      { category: 'football' },
      { category: 'transfer' },
      { category: 'match' },
      { category: 'interview' },
      { category: 'league' },
    ];
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { category } = await params;
  const resolvedSearchParams = await searchParams;
  
  // Pass category and search params to NewsPage component
  return (
    <NewsPage 
      initialCategory={category}
      initialSearchParams={resolvedSearchParams}
    />
  );
}
