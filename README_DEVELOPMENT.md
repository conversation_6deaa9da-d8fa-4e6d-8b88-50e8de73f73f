# 🏗️ Sports News Platform - Development Documentation

## 📋 Project Overview

**Sports News Platform** is a modern Next.js 15 application built with TypeScript, featuring a hybrid feature-module architecture with advanced version management system.

### 🎯 Key Features
- **Real-time Sports Data**: Live fixtures, scores, and news
- **Advanced UI/UX**: Multiple component versions with 3D effects
- **SEO Optimized**: Dynamic metadata and structured data
- **Performance Focused**: Caching, code splitting, and optimization
- **Mobile-First**: Responsive design with mobile optimizations

---

## 📚 Documentation Index

### 🏗️ Architecture Documentation
- **[SYSTEM_ARCHITECTURE_MAP.md](./SYSTEM_ARCHITECTURE_MAP.md)** - Complete system architecture and workflow
- **[COMPONENT_MAPPING.md](./COMPONENT_MAPPING.md)** - Component relationships and dependencies
- **[API_DOCUMENTATION.md](./API_DOCUMENTATION.md)** - API endpoints and integration guide
- **[.augment-rules.md](./.augment-rules.md)** - Architecture rules and guidelines
- **[REFACTOR_SUMMARY.md](./REFACTOR_SUMMARY.md)** - Refactoring history and changes

### 🧩 Component Documentation
- **[Hero Components README](./src/features/home/<USER>/hero/README.md)** - Hero section versions (v1-v5)
- **[Magnetic Field System](./src/features/home/<USER>/README.md)** - Advanced 3D effects documentation
- **Individual Component READMEs** - Located in each component directory

---

## 🚀 Quick Start Guide

### Prerequisites
- **Node.js**: 18+ 
- **npm**: 9+
- **Backend API**: Running on localhost:3000

### Installation
```bash
# Clone repository
git clone <repository-url>
cd sports-news-platform

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

### Environment Variables
```bash
# Required
NEXT_PUBLIC_CDN_DOMAIN_PICTURE=http://**************/
BASE_URL=http://localhost:3000
BACKEND_API_URL=http://localhost:3000
PORT=5000

# Optional
NODE_ENV=development
```

### Development URLs
- **Frontend**: http://localhost:5000
- **Backend API**: http://localhost:3000
- **News Page**: http://localhost:5000/news
- **API Endpoints**: http://localhost:5000/api/*

---

## 🏗️ Architecture Overview

### Directory Structure
```
src/
├── app/                    # Next.js App Router (Routing Layer)
├── features/               # Feature Modules (Business Domains)
│   ├── home/              # Home feature with 5 hero versions
│   ├── news/              # News feature with 2-column layout
│   ├── fixture/           # Fixture feature (planned)
│   └── league/            # League feature (planned)
├── shared/                # Global Shared Resources
├── lib/                   # External Integrations
└── types/                 # Global Type Definitions
```

### Feature Module Pattern
Each feature follows this structure:
```
feature/
├── config.ts              # Version configuration
├── index.ts               # Feature exports
├── types.ts               # Feature types
├── pages/                 # Page components
├── components/            # Feature components
└── hooks/                 # Feature hooks
```

### Version Management
```typescript
// Configuration-based versioning
export const homeFeatureConfig = {
  components: {
    hero: 'v5',               // Switch versions easily
    upcomingFixtures: 'v2'
  }
} as const;
```

---

## 🔌 API Integration

### Proxy Layer Architecture
```
Frontend → Next.js API Routes → Backend API → Database
```

### Key APIs
- **Football Fixtures**: `/api/football/fixtures`
- **Football Leagues**: `/api/football/leagues`
- **News Articles**: `/api/news`
- **News Categories**: `/api/news/categories`
- **Article Detail**: `/api/news/article/[slug]`
- **Related Articles**: `/api/news/related/[slug]`

### Fallback Strategy
1. **Try Real Backend API** (multiple endpoints)
2. **Fallback to Mock Data** (development)
3. **Return Error State** (graceful degradation)

---

## 🧩 Component System

### Component Hierarchy
1. **Global Shared Components** (`src/shared/components/`)
2. **Feature-Level Components** (`src/features/*/components/`)
3. **Page-Level Components** (`src/features/*/pages/*/components/`)

### Current Components

#### Home Feature
- **Hero Section**: 5 versions (v1-v5) with progressive enhancements
- **Breaking News**: v1 with carousel and real API integration
- **Upcoming Fixtures**: v2 with enhanced filtering and pagination
- **Engagement Features**: v2 with glassmorphism and real-time updates
- **Footer Content**: v2 with minimalist design

#### News Feature
- **News Page**: v1 with 2-column layout (categories + news grid)
- **News Detail**: v1 with article content and related articles
- **Components**: NewsCard, CategorySidebar, SearchBar, FilterBar

---

## 🎨 Design System

### Styling
- **Framework**: Tailwind CSS
- **Design Tokens**: Consistent colors, spacing, typography
- **Responsive**: Mobile-first approach
- **Dark Mode**: Theme support (planned)

### UI Patterns
- **2-Column Layouts**: Categories sidebar + content area
- **Card-Based Design**: Consistent card components
- **Progressive Enhancement**: Version-based improvements
- **Glassmorphism**: Modern UI effects in v2 components

---

## ⚡ Performance Optimization

### Caching Strategy
- **API Routes**: 30s-5min cache with stale-while-revalidate
- **Static Assets**: CDN integration with Next.js Image
- **Code Splitting**: Feature-based and route-based splitting

### Mobile Optimization
- **Responsive Design**: Mobile-first breakpoints
- **Performance Mode**: Reduced animations on mobile
- **Battery Optimization**: Automatic detection and optimization

### Bundle Optimization
- **Dynamic Imports**: Version-based component loading
- **Tree Shaking**: Unused code elimination
- **Image Optimization**: Next.js automatic optimization

---

## 🔧 Development Workflow

### Adding New Features
1. **Create Feature Directory**: `src/features/<feature-name>/`
2. **Add Configuration**: Version management setup
3. **Implement Components**: Following established patterns
4. **Add API Integration**: Using proxy layer
5. **Update Routing**: Next.js App Router integration

### Version Management Workflow
1. **Update Config**: Change version number
2. **Create New Version**: Copy and enhance previous version
3. **Test Thoroughly**: Ensure backward compatibility
4. **Deploy Gradually**: Environment-based rollout

### Code Quality
- **TypeScript**: Strict type checking
- **ESLint**: Code linting and formatting
- **Component Testing**: Unit and integration tests
- **Performance Testing**: Lighthouse audits

---

## 🐛 Debugging Guide

### Common Issues

#### API Connection Issues
```bash
# Check backend connectivity
curl http://localhost:3000/news/categories
curl http://localhost:3000/football/fixtures

# Check environment variables
echo $NEXT_PUBLIC_CDN_DOMAIN_PICTURE
echo $BACKEND_API_URL
```

#### Version Management Issues
1. Check config file version numbers
2. Verify component directory structure
3. Test dynamic imports
4. Check index.ts exports

#### Image Loading Issues
- Verify CDN environment variable
- Check image paths in API responses
- Test fallback image handling

### Debug Tools
- **Next.js DevTools**: Built-in debugging
- **Chrome DevTools**: Performance profiling
- **Network Tab**: API request monitoring
- **Console Logs**: Detailed API logging

---

## 🚀 Deployment

### Build Process
```bash
npm run build              # Build production bundle
npm run start              # Start production server
npm run lint               # Code quality check
```

### Environment Setup
```bash
# Production environment variables
NEXT_PUBLIC_CDN_DOMAIN_PICTURE=https://cdn.yourdomain.com/
BASE_URL=https://api.yourdomain.com
BACKEND_API_URL=https://api.yourdomain.com
NODE_ENV=production
```

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Backend API accessible
- [ ] CDN images accessible
- [ ] SSL certificates installed
- [ ] Performance monitoring enabled
- [ ] Error tracking configured

---

## 📊 Current Status

### ✅ Completed Features
- **Home Feature**: Complete with 5 hero versions and advanced components
- **News Feature**: Complete with 2-column layout and real API integration
- **API Proxy Layer**: Complete with fallback strategy
- **Version Management**: Complete system implementation
- **Performance Optimization**: Caching, code splitting, mobile optimization

### 🚧 In Development
- **Fixture Feature**: Configuration ready, implementation planned
- **League Feature**: Configuration ready, implementation planned
- **Advanced Analytics**: Performance monitoring enhancements
- **SEO Enhancement**: Advanced metadata optimization

### 📋 Planned Features
- **User Authentication**: Login/register system
- **Personalization**: User preferences and favorites
- **Real-time Updates**: WebSocket integration
- **Progressive Web App**: PWA capabilities
- **Dark Mode**: Theme switching system

---

## 🤝 Contributing

### Development Standards
- **Code Style**: Follow ESLint configuration
- **TypeScript**: Strict typing required
- **Component Structure**: Follow established patterns
- **Documentation**: Update relevant documentation
- **Testing**: Add tests for new features

### Pull Request Process
1. **Create Feature Branch**: `feature/feature-name`
2. **Implement Changes**: Following coding standards
3. **Add Tests**: Unit and integration tests
4. **Update Documentation**: Keep docs current
5. **Submit PR**: With detailed description

---

## 📞 Support

### Documentation Resources
- **Architecture**: [SYSTEM_ARCHITECTURE_MAP.md](./SYSTEM_ARCHITECTURE_MAP.md)
- **Components**: [COMPONENT_MAPPING.md](./COMPONENT_MAPPING.md)
- **APIs**: [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)
- **Rules**: [.augment-rules.md](./.augment-rules.md)

### Getting Help
- **Issues**: Create GitHub issues for bugs
- **Features**: Discuss new features in issues
- **Questions**: Check documentation first
- **Code Review**: Submit PRs for review

---

*This development documentation serves as the main entry point for understanding and contributing to the Sports News Platform.*
