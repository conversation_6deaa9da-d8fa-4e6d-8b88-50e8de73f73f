# News Pages Restructure - V2 Implementation

## Overview
Đã tái cấu trúc hoàn toàn hệ thống News Pages theo yêu cầu với khung sườn chung, hiệu năng cao và layout tối ưu.

## New Architecture

### 1. **Shared Layout Component** (`src/features/news/layouts/NewsLayout.tsx`)

#### Features:
- **Khung sườn chung** cho tất cả news pages
- **Left Sidebar**: Categories với sticky positioning
- **Right Content**: Dynamic content area
- **Responsive Design**: Mobile-first approach
- **Performance Optimized**: Memoization và lazy loading
- **Search Integration**: Optional search functionality

#### Usage:
```tsx
<NewsLayout
  selectedCategory="football"
  onCategoryChange={handleCategoryChange}
  showSearch={true}
>
  <YourContentComponent />
</NewsLayout>
```

### 2. **News List Page V2** (`src/features/news/pages/news-page/v2/NewsPageV2.tsx`)

#### Features:
- **10 articles per page** với number pagination
- **Category filtering** via URL parameters
- **Search functionality** với URL state management
- **Grid/List variants** cho display flexibility
- **SEO optimized** với structured data
- **URL-based state** cho bookmarking và sharing

#### Layout Structure:
```
┌─────────────────────────────────────────┐
│ Page Header (Sports News)               │
├─────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────────────────────┐ │
│ │Categories│ │ Content Header          │ │
│ │ (Sticky)│ │ ┌─────────────────────┐ │ │
│ │         │ │ │ News Articles       │ │ │
│ │ Search  │ │ │ (10 per page)       │ │ │
│ │         │ │ │ Grid/List Layout    │ │ │
│ │         │ │ └─────────────────────┘ │ │
│ │         │ │ ┌─────────────────────┐ │ │
│ │         │ │ │ Number Pagination   │ │ │
│ │         │ │ └─────────────────────┘ │ │
│ └─────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 3. **News Detail Page V2** (`src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx`)

#### Features:
- **Shared layout** với news list page
- **Category navigation** consistency
- **Article content** với enhanced styling
- **Related articles** section
- **Social sharing** integration
- **Reading analytics** tracking
- **SEO optimized** với structured data

#### Layout Structure:
```
┌─────────────────────────────────────────┐
│ Page Header (Sports News)               │
├─────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────────────────────┐ │
│ │Categories│ │ Breadcrumb Navigation   │ │
│ │ (Sticky)│ │ ┌─────────────────────┐ │ │
│ │         │ │ │ Article Header      │ │ │
│ │         │ │ │ Featured Image      │ │ │
│ │         │ │ │ Article Content     │ │ │
│ │         │ │ │ Social Share        │ │ │
│ │         │ │ │ Tags               │ │ │
│ │         │ │ └─────────────────────┘ │ │
│ │         │ │ ┌─────────────────────┐ │ │
│ │         │ │ │ Related Articles    │ │ │
│ │         │ │ └─────────────────────┘ │ │
│ └─────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────┘
```

## Key Improvements

### 1. **Performance Optimizations**
- **Shared Layout**: Tránh duplicate code và improve consistency
- **Memoization**: React.memo cho expensive components
- **Lazy Loading**: Dynamic imports cho version management
- **Caching**: Enhanced caching strategy với TTL
- **URL State Management**: Efficient state persistence

### 2. **User Experience**
- **Consistent Navigation**: Categories sidebar luôn available
- **URL-based Filtering**: Bookmarkable và shareable URLs
- **Responsive Design**: Mobile-first với adaptive layouts
- **Loading States**: Skeleton loading cho better perceived performance
- **Error Handling**: Graceful error states với retry functionality

### 3. **SEO & Accessibility**
- **Structured Data**: Rich snippets cho search engines
- **Meta Tags**: Dynamic meta information
- **Semantic HTML**: Proper heading hierarchy
- **ARIA Labels**: Screen reader friendly
- **Keyboard Navigation**: Full keyboard accessibility

### 4. **Developer Experience**
- **Version Management**: Easy switching between versions
- **Type Safety**: Full TypeScript support
- **Component Reusability**: Shared components across pages
- **Configuration**: Centralized config management
- **Documentation**: Comprehensive inline documentation

## URL Structure

### News List Page:
- `/news` - All articles (page 1)
- `/news?category=football` - Football articles
- `/news?search=transfer` - Search results
- `/news?category=football&page=2` - Football articles page 2
- `/news?category=football&search=messi&page=3` - Combined filters

### News Detail Page:
- `/news/{slug}` - Article detail
- Category navigation maintains context

## Component Hierarchy

```
NewsLayout (Shared)
├── CategorySidebar (Enhanced with loading)
├── SearchBar (Optional)
└── Children (Dynamic Content)
    ├── NewsPageV2
    │   ├── NewsList (Grid/List variants)
    │   ├── Pagination (Number-based)
    │   └── StructuredData
    └── NewsDetailPageV2
        ├── Article Content
        ├── Social Share
        ├── Related Articles
        └── Reading Analytics
```

## Configuration Updates

### Version Management:
```typescript
// config.ts
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v2',     // Restructured layout
    newsDetail: 'v2',   // Restructured layout
  }
};
```

### Feature Flags:
- **Pagination**: Number-based (10 per page)
- **Layout**: Shared layout component
- **Search**: URL-based state management
- **Categories**: Sticky sidebar navigation
- **Performance**: Memoization enabled

## Migration Path

### From V1 to V2:
1. **Automatic**: Version switching via config
2. **Backward Compatible**: V1 components still available
3. **Gradual Migration**: Can switch individual pages
4. **Fallback Support**: Error boundaries với V1 fallback

### Breaking Changes:
- **URL Structure**: New query parameter format
- **Component Props**: Enhanced prop interfaces
- **State Management**: URL-based instead of local state

## Performance Metrics

### Expected Improvements:
- **Initial Load**: 20-30% faster due to shared layout
- **Navigation**: 50-70% faster due to layout persistence
- **Memory Usage**: 15-25% reduction due to component reuse
- **Bundle Size**: 10-15% smaller due to code deduplication

### Monitoring:
- **Core Web Vitals**: LCP, FID, CLS tracking
- **User Analytics**: Page views, bounce rate, session duration
- **Performance**: Load times, cache hit rates
- **Error Tracking**: Component error boundaries

## Files Created/Modified

### New Files:
- `src/features/news/layouts/NewsLayout.tsx`
- `src/features/news/pages/news-page/v2/NewsPageV2.tsx`
- `src/features/news/pages/news-page/v2/index.ts`
- `src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx`
- `src/features/news/pages/news-detail-page/v2/index.ts`

### Modified Files:
- `src/features/news/config.ts` - Updated to V2
- `src/features/news/pages/news-page/index.ts` - Version switch
- `src/features/news/pages/news-detail-page/index.ts` - Version switch
- `src/features/news/pages/news-page/v1/components/CategorySidebar.tsx` - Added loading state
- `src/features/news/pages/news-page/v1/components/NewsList.tsx` - Added grid variant

## Testing Strategy

### Unit Tests:
- Layout component rendering
- Category filtering logic
- Pagination functionality
- URL state management

### Integration Tests:
- Page navigation flows
- Search functionality
- Category filtering
- Responsive behavior

### E2E Tests:
- Complete user journeys
- Performance benchmarks
- SEO validation
- Accessibility compliance

## Next Steps

1. **Performance Monitoring**: Set up metrics tracking
2. **User Feedback**: Collect user experience data
3. **A/B Testing**: Compare V1 vs V2 performance
4. **Feature Enhancement**: Add advanced filtering options
5. **Mobile Optimization**: Further mobile UX improvements

The restructured news system provides a solid foundation for scalable, performant, and maintainable news functionality with consistent user experience across all news-related pages.
