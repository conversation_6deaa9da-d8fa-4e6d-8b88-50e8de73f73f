// News Feature - Main Exports
// This file exports all public APIs for the news feature

// Configuration
export { newsFeatureConfig, getCurrentNewsConfig, getPageVersion, getComponentVersion } from './config';

// Pages
export { NewsPage } from './pages';
export { NewsDetailPage } from './pages';

// Components (from pages - can be used externally)
export { NewsCard } from './pages/news-page/v1/components/NewsCard';
export { CategorySidebar } from './pages/news-page/v1/components/CategorySidebar';
export { SearchBar } from './pages/news-page/v1/components/SearchBar';

// Hooks
export { useNews } from './pages/news-page/v1/hooks/useNews';

// Services
export { NewsService } from './services/NewsService';
export { NewsCacheService } from './services/NewsCacheService';

// Utilities
export * from './utils/imageUtils';
export * from './utils/errorUtils';
export * from './utils/categoryUtils';
export * from './utils/apiUtils';

// Types
export type { NewsFeatureConfig } from './config';
export type * from './types';
