# News Utilities

This directory contains shared utilities for the news feature, designed to eliminate code duplication and provide consistent behavior across components.

## Overview

The utilities are organized into four main modules:

- **`imageUtils.ts`** - Image handling and optimization
- **`errorUtils.ts`** - Error handling and user feedback
- **`categoryUtils.ts`** - Category management and display
- **`apiUtils.ts`** - API request handling and responses

## Image Utils (`imageUtils.ts`)

### Functions

#### `getImageUrl(featuredImage, fallbackText?)`
Generates optimized image URLs with CDN support and fallback handling.

```typescript
import { getImageUrl } from '@/features/news/utils/imageUtils';

// With CDN path
const imageUrl = getImageUrl('/images/article.jpg');
// Returns: https://cdn.example.com/images/article.jpg

// With null image
const placeholderUrl = getImageUrl(null, 'News+Article');
// Returns: https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=News+Article
```

#### `handleImageError(event, fallbackText?)`
Standardized image error handling for React components.

```typescript
<img
  src={imageUrl}
  alt="Article"
  onError={(e) => handleImageError(e, 'News+Image')}
/>
```

#### `optimizeImageUrl(imageUrl, width?, quality?)`
Adds optimization parameters to image URLs.

```typescript
const optimized = optimizeImageUrl(originalUrl, 400, 80);
// Adds ?w=400&q=80 to the URL
```

## Error Utils (`errorUtils.ts`)

### Functions

#### `parseNewsError(error)`
Normalizes errors from different sources into a consistent format.

```typescript
import { parseNewsError } from '@/features/news/utils/errorUtils';

try {
  await fetchNews();
} catch (error) {
  const normalizedError = parseNewsError(error);
  console.log(normalizedError.message, normalizedError.code);
}
```

#### `getUserFriendlyErrorMessage(error)`
Converts technical errors into user-friendly messages.

```typescript
const userMessage = getUserFriendlyErrorMessage({
  message: 'ECONNREFUSED',
  code: 'NETWORK_ERROR'
});
// Returns: "Network connection error. Please check your internet connection."
```

#### `retryWithBackoff(fn, maxRetries?, baseDelay?)`
Retry mechanism with exponential backoff for failed operations.

```typescript
const result = await retryWithBackoff(
  () => fetchNews({ page: 1 }),
  3, // max retries
  1000 // base delay in ms
);
```

## Category Utils (`categoryUtils.ts`)

### Functions

#### `mergeCategories(apiCategories)`
Merges API categories with default categories, ensuring "All News" is always first.

```typescript
import { mergeCategories } from '@/features/news/utils/categoryUtils';

const allCategories = mergeCategories(apiCategories);
// Returns: [{ slug: 'all', name: 'All News', ... }, ...apiCategories]
```

#### `getCategoryIcon(slug)` & `getCategoryColors(slug)`
Get consistent icons and colors for category slugs.

```typescript
const icon = getCategoryIcon('match'); // Returns: ⚽
const colors = getCategoryColors('breaking'); // Returns: bg-red-100 text-red-800
```

#### `formatCategoryDisplayName(category, showCount?)`
Format category names with optional article counts.

```typescript
const displayName = formatCategoryDisplayName(
  { name: 'Football', count: 25 },
  true
);
// Returns: "Football (25)"
```

## API Utils (`apiUtils.ts`)

### Functions

#### `createErrorResponse(error, statusCode?)`
Creates standardized error responses for API routes.

```typescript
import { createErrorResponse } from '@/features/news/utils/apiUtils';

export async function GET() {
  try {
    // ... API logic
  } catch (error) {
    return createErrorResponse(error, 500);
  }
}
```

#### `tryMultipleEndpoints(endpoints, options?)`
Attempts multiple backend endpoints with fallback.

```typescript
const response = await tryMultipleEndpoints([
  'https://api1.example.com/news',
  'https://api2.example.com/news',
  'https://fallback.example.com/news'
]);
```

#### `parseQueryParams(searchParams)`
Parses and validates URL search parameters.

```typescript
const { page, limit, category, search } = parseQueryParams(searchParams);
// Ensures page >= 1, limit <= 100, etc.
```

#### `buildQueryString(params)`
Builds query strings from parameter objects, filtering out empty values.

```typescript
const queryString = buildQueryString({
  page: 1,
  limit: 20,
  category: 'football',
  empty: '', // filtered out
  undefined: undefined // filtered out
});
// Returns: "page=1&limit=20&category=football"
```

## Usage Examples

### In Components

```typescript
import React from 'react';
import { 
  getImageUrl, 
  handleImageError,
  getCategoryIcon,
  getCategoryColors 
} from '@/features/news/utils';

export const NewsCard = ({ article }) => {
  const imageUrl = getImageUrl(article.featuredImage);
  const categoryIcon = getCategoryIcon(article.category.slug);
  const categoryColors = getCategoryColors(article.category.slug);

  return (
    <div>
      <img
        src={imageUrl}
        alt={article.title}
        onError={(e) => handleImageError(e, 'News+Image')}
      />
      <span className={categoryColors}>
        {categoryIcon} {article.category.name}
      </span>
    </div>
  );
};
```

### In API Routes

```typescript
import { 
  createErrorResponse,
  createSuccessResponse,
  parseQueryParams,
  tryMultipleEndpoints 
} from '@/features/news/utils/apiUtils';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const { page, limit, category } = parseQueryParams(searchParams);

    const response = await tryMultipleEndpoints([
      `${baseUrl}/news?page=${page}&limit=${limit}`,
      `${fallbackUrl}/news?page=${page}&limit=${limit}`
    ]);

    const data = await response.json();
    return createSuccessResponse(data, 60); // 60s cache

  } catch (error) {
    return createErrorResponse(error);
  }
}
```

### In Hooks

```typescript
import { parseNewsError, retryWithBackoff } from '@/features/news/utils/errorUtils';

export const useNews = () => {
  const [error, setError] = useState(null);

  const fetchArticles = async () => {
    try {
      const data = await retryWithBackoff(() => fetchNews());
      return data;
    } catch (err) {
      const normalizedError = parseNewsError(err);
      setError(normalizedError);
      throw normalizedError;
    }
  };

  return { fetchArticles, error };
};
```

## Benefits

1. **Consistency** - All components use the same logic for common operations
2. **Maintainability** - Changes to shared logic only need to be made in one place
3. **Testing** - Utilities can be tested in isolation
4. **Performance** - Optimized implementations with caching and error handling
5. **Developer Experience** - Clear, documented APIs for common tasks

## Testing

Run the utility tests with:

```bash
npm test src/features/news/utils/__tests__/optimization.test.ts
```

The tests cover all utility functions and their edge cases, ensuring reliability and correctness.
