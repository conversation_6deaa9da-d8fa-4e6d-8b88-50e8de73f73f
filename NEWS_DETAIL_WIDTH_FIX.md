# News Detail Page Width Consistency Fix

## Problem Analysis

Đã phát hiện vấn đề về chiều rộng không đồng bộ giữa các trang news detail:
- `http://localhost:5000/news/b-quy-nh-mi-gia-nh-ch-c-sinh-ti-a-hai-con`
- `http://localhost:5000/news/iu-g-xy-ra-khi-n-ni-tng-tht-ti-sng`

## Root Causes Identified

### 1. **Content Overflow Issues**
- Article content có thể chứa elements rộng (tables, images, code blocks)
- Không có constraints đầy đủ để prevent overflow
- CSS grid layout có thể bị ảnh hưởng bởi content width

### 2. **Missing CSS Constraints**
- `.article-content` không có `max-width` và `overflow` handling
- Tables có `w-full` nhưng không có `table-layout: fixed`
- Images và media elements thiếu proper sizing constraints

### 3. **Grid Layout Issues**
- Grid containers thiếu `min-w-0` để prevent overflow
- Column spans có thể bị ảnh hưởng bởi content width

## Solutions Implemented

### 1. **Enhanced Article Content CSS** (`src/features/news/pages/news-detail-page/v1/styles/article-content.css`)

#### Base Content Container
```css
.article-content {
  @apply text-gray-900 leading-relaxed;
  /* Ensure content never overflows container */
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}
```

#### Table Handling
```css
.article-content table {
  @apply w-full border-collapse border border-gray-300 my-6 overflow-x-auto;
  max-width: 100%;
  table-layout: fixed;
}
```

#### Code Block Improvements
```css
.article-content pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
  max-width: 100%;
}

.article-content pre code {
  @apply bg-transparent p-0;
  white-space: pre-wrap;
  word-break: break-all;
}
```

#### Image Constraints
```css
.article-content img {
  @apply max-w-full h-auto rounded-lg shadow-sm my-6;
  width: auto;
  height: auto;
}
```

#### Universal Element Constraints
```css
/* Handle wide content elements */
.article-content * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Specific handling for potentially wide elements */
.article-content iframe,
.article-content video,
.article-content embed,
.article-content object {
  max-width: 100%;
  height: auto;
}
```

### 2. **Layout Container Improvements** (`src/features/news/pages/news-detail-page/v1/NewsDetailPageV1.tsx`)

#### Grid Container
```tsx
<div className="grid grid-cols-1 lg:grid-cols-4 gap-8 min-w-0">
```

#### Article Column
```tsx
<div className="lg:col-span-3 min-w-0">
```

#### Article Content Container
```tsx
<article className="prose prose-lg max-w-none overflow-hidden">
  <div className="text-gray-900 leading-relaxed">
    <div
      className="article-content overflow-hidden"
      dangerouslySetInnerHTML={{ __html: article.content }}
    />
  </div>
</article>
```

## Technical Details

### CSS Properties Used

1. **`min-w-0`**: Prevents grid items from expanding beyond container
2. **`overflow-hidden`**: Clips content that exceeds container bounds
3. **`max-width: 100%`**: Ensures no element exceeds parent width
4. **`table-layout: fixed`**: Makes tables respect width constraints
5. **`overflow-wrap: break-word`**: Breaks long words to prevent overflow
6. **`box-sizing: border-box`**: Includes padding/border in width calculations

### Layout Structure

```
max-w-7xl container
├── grid grid-cols-4 (with min-w-0)
    ├── col-span-1 (sidebar)
    └── col-span-3 (article, with min-w-0)
        └── article content (with overflow-hidden)
            └── .article-content (with width constraints)
```

## Benefits

### 1. **Consistent Width**
- All news detail pages now have identical layout width
- Content overflow is properly contained
- Grid layout remains stable regardless of content

### 2. **Responsive Design**
- Tables scroll horizontally on small screens
- Images scale properly on all devices
- Text wraps correctly without breaking layout

### 3. **Performance**
- No layout shifts due to content loading
- Stable grid calculations
- Improved rendering performance

### 4. **Accessibility**
- Content remains readable on all screen sizes
- Proper text wrapping for screen readers
- Maintained semantic structure

## Testing

### Scenarios Covered
1. **Long tables**: Tables with many columns
2. **Wide images**: Images wider than container
3. **Long URLs**: Unbreakable text content
4. **Code blocks**: Pre-formatted content
5. **Mixed content**: Articles with various element types

### Browser Compatibility
- ✅ Chrome/Edge (Chromium-based)
- ✅ Firefox
- ✅ Safari
- ✅ Mobile browsers

## Maintenance Notes

### CSS Specificity
- Article content styles use utility classes where possible
- Custom CSS properties are scoped to `.article-content`
- No global style modifications

### Future Considerations
1. **Content Validation**: Consider server-side content sanitization
2. **Performance Monitoring**: Track layout shift metrics
3. **Content Guidelines**: Establish content width guidelines for editors

## Files Modified

1. **`src/features/news/pages/news-detail-page/v1/styles/article-content.css`**
   - Enhanced content constraints
   - Added overflow handling
   - Improved responsive design

2. **`src/features/news/pages/news-detail-page/v1/NewsDetailPageV1.tsx`**
   - Added `min-w-0` to grid containers
   - Added `overflow-hidden` to content areas
   - Improved layout stability

## Verification

To verify the fix:
1. Visit both news detail pages
2. Check that both pages have identical layout width
3. Test with different content types (tables, images, long text)
4. Verify responsive behavior on mobile devices
5. Check browser developer tools for layout consistency

The fix ensures that all news detail pages maintain consistent width regardless of their content, providing a uniform user experience across the application.
