// News API Client - Utility functions for news API calls via proxy
// This module provides a clean interface for news-related API operations

export interface NewsAPIParams {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  // Note: Backend doesn't support 'featured' or 'status' filtering
  // All articles are published, featured status is determined by isFeatured field
}

export interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  tags: string[];
  status: string;
  publishedAt: string;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isFeatured: boolean;
  priority: number;
  category: {
    id: number;
    slug: string;
    name: string;
    description: string;
    icon: string;
    color: string;
  };
  authorId: number;
  createdAt: string;
  updatedAt: string;
}

export interface NewsAPIResponse {
  data: NewsArticle[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
    hasNextPage?: boolean;
    hasPreviousPage?: boolean;
    nextPage?: number | null;
    previousPage?: number | null;
  };
  status: number;
}

export interface NewsAPIError {
  error: string;
  message: string;
  code: string;
}

/**
 * Fetch news articles via Next.js API proxy
 * @param params - Query parameters for filtering news
 * @returns Promise<NewsAPIResponse>
 */
export async function fetchNews(params: NewsAPIParams = {}): Promise<NewsAPIResponse> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.category) queryParams.append('category', params.category);
    if (params.search) queryParams.append('search', params.search);

    // Note: Backend doesn't support 'featured' or 'status' filtering
    // All returned articles are published, featured status is in isFeatured field

    const url = `/api/news?${queryParams.toString()}`;

    console.log('📰 Fetching news via proxy:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for better performance
      cache: 'default',
    });

    if (!response.ok) {
      const errorData: NewsAPIError = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: 'HTTP_ERROR'
      }));

      throw new Error(errorData.message || `Failed to fetch news: ${response.status}`);
    }

    const data: NewsAPIResponse = await response.json();

    console.log('✅ News fetched successfully:', data.data.length, 'articles');

    return data;

  } catch (error) {
    console.error('❌ Error fetching news:', error);

    // Re-throw with enhanced error information
    if (error instanceof Error) {
      throw new Error(`News API Error: ${error.message}`);
    }

    throw new Error('Unknown error occurred while fetching news');
  }
}

/**
 * Fetch featured news articles via dedicated featured endpoint
 * @param limit - Number of articles to fetch (default: 5)
 * @returns Promise<NewsAPIResponse>
 */
export async function fetchFeaturedNews(limit: number = 5): Promise<NewsAPIResponse> {
  try {
    // Use dedicated featured endpoint
    const url = `/api/news/featured?limit=${limit}`;

    console.log('📰 Fetching featured news via proxy:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'default',
    });

    if (!response.ok) {
      const errorData: NewsAPIError = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: 'HTTP_ERROR'
      }));

      throw new Error(errorData.message || `Failed to fetch featured news: ${response.status}`);
    }

    const data: NewsAPIResponse = await response.json();

    console.log('✅ Featured news fetched successfully:', data.data.length, 'articles');

    return data;

  } catch (error) {
    console.error('❌ Error fetching featured news:', error);

    // Fallback to regular news with client-side filtering
    console.log('🔄 Falling back to regular news with client-side filtering');
    const response = await fetchNews({
      limit: Math.max(limit * 3, 10) // Fetch more to ensure we get enough featured articles
    });

    // Filter for featured articles
    const featuredArticles = response.data.filter(article => article.isFeatured).slice(0, limit);

    return {
      ...response,
      data: featuredArticles,
      meta: {
        ...response.meta,
        totalItems: featuredArticles.length,
        totalPages: 1,
        currentPage: 1,
        limit: featuredArticles.length
      }
    };
  }
}

/**
 * Fetch news by category using dedicated category endpoint
 * @param category - Category slug to filter by
 * @param limit - Number of articles to fetch (default: 10)
 * @returns Promise<NewsAPIResponse>
 */
export async function fetchNewsByCategory(category: string, limit: number = 10): Promise<NewsAPIResponse> {
  try {
    // Use dedicated category endpoint
    const url = `/api/news/category/${category}?limit=${limit}`;

    console.log('📰 Fetching news by category via proxy:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'default',
    });

    if (!response.ok) {
      const errorData: NewsAPIError = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: 'HTTP_ERROR'
      }));

      throw new Error(errorData.message || `Failed to fetch news by category: ${response.status}`);
    }

    const data: NewsAPIResponse = await response.json();

    console.log('✅ News by category fetched successfully:', data.data.length, 'articles');

    return data;

  } catch (error) {
    console.error('❌ Error fetching news by category:', error);

    // Fallback to regular news with category filter
    console.log('🔄 Falling back to regular news with category filter');
    return fetchNews({
      category,
      limit
    });
  }
}

/**
 * Search news articles
 * @param query - Search query string
 * @param limit - Number of articles to fetch (default: 10)
 * @returns Promise<NewsAPIResponse>
 */
export async function searchNews(query: string, limit: number = 10): Promise<NewsAPIResponse> {
  return fetchNews({
    search: query,
    limit
  });
}

/**
 * Fetch breaking news (high priority featured articles)
 * @param limit - Number of articles to fetch (default: 4)
 * @returns Promise<NewsAPIResponse>
 */
export async function fetchBreakingNews(limit: number = 4): Promise<NewsAPIResponse> {
  // Since backend doesn't support featured filtering, we fetch more articles and filter client-side
  const response = await fetchNews({
    limit: Math.max(limit * 4, 10) // Fetch more to ensure we get enough breaking news
  });

  // Filter for featured articles with high priority (breaking news)
  const breakingArticles = response.data
    .filter(article => article.isFeatured && article.priority >= 7)
    .slice(0, limit);

  // If not enough breaking news, fall back to just featured articles
  if (breakingArticles.length < limit) {
    const featuredArticles = response.data
      .filter(article => article.isFeatured)
      .slice(0, limit);

    return {
      ...response,
      data: featuredArticles,
      meta: {
        ...response.meta,
        totalItems: featuredArticles.length,
        totalPages: 1,
        currentPage: 1,
        limit: featuredArticles.length
      }
    };
  }

  return {
    ...response,
    data: breakingArticles,
    meta: {
      ...response.meta,
      totalItems: breakingArticles.length,
      totalPages: 1,
      currentPage: 1,
      limit: breakingArticles.length
    }
  };
}

/**
 * Check news API health via proxy
 * @returns Promise<boolean>
 */
export async function checkNewsAPIHealth(): Promise<boolean> {
  try {
    const response = await fetch('/api/news', {
      method: 'HEAD',
      cache: 'no-cache'
    });

    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get news API status information
 * @returns Promise<{healthy: boolean, message: string}>
 */
export async function getNewsAPIStatus(): Promise<{ healthy: boolean, message: string }> {
  try {
    const healthy = await checkNewsAPIHealth();

    if (healthy) {
      return {
        healthy: true,
        message: 'News API is operational'
      };
    } else {
      return {
        healthy: false,
        message: 'News API is currently unavailable'
      };
    }
  } catch (error) {
    return {
      healthy: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Increment article view count
 * @param articleId - Article ID or slug
 * @returns Promise<{success: boolean, viewCount: number}>
 */
export async function incrementViewCount(articleId: string): Promise<{ success: boolean; viewCount: number }> {
  try {
    const url = `/api/news/${articleId}/view`;

    console.log('📰 Incrementing view count via proxy:', url);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-cache',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
      }));

      throw new Error(errorData.message || `Failed to increment view count: ${response.status}`);
    }

    const data = await response.json();

    console.log('✅ View count incremented successfully:', data.viewCount);

    return {
      success: data.success || true,
      viewCount: data.viewCount || 0
    };

  } catch (error) {
    console.error('❌ Error incrementing view count:', error);
    return {
      success: false,
      viewCount: 0
    };
  }
}

/**
 * Increment article share count
 * @param articleId - Article ID or slug
 * @returns Promise<{success: boolean, shareCount: number}>
 */
export async function incrementShareCount(articleId: string): Promise<{ success: boolean; shareCount: number }> {
  try {
    const url = `/api/news/${articleId}/share`;

    console.log('📰 Incrementing share count via proxy:', url);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-cache',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
      }));

      throw new Error(errorData.message || `Failed to increment share count: ${response.status}`);
    }

    const data = await response.json();

    console.log('✅ Share count incremented successfully:', data.shareCount);

    return {
      success: data.success || true,
      shareCount: data.shareCount || 0
    };

  } catch (error) {
    console.error('❌ Error incrementing share count:', error);
    return {
      success: false,
      shareCount: 0
    };
  }
}
