// News Image Utilities - Shared utilities for image handling
// This module provides consistent image handling across news components

/**
 * Get optimized image URL with fallback handling
 * @param featuredImage - Image path from API (can be null, relative path, or full URL)
 * @param fallbackText - Text to display in placeholder (default: 'News Image')
 * @returns Optimized image URL
 */
export const getImageUrl = (
  featuredImage: string | null, 
  fallbackText: string = 'News+Image'
): string => {
  // Return placeholder for null/empty images
  if (!featuredImage) {
    return `https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=${fallbackText}`;
  }

  // If it's already a full URL (starts with http), use it directly
  if (featuredImage.startsWith('http')) {
    return featuredImage;
  }

  // If it's a relative path, prepend CDN domain
  const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
  if (cdnDomain) {
    return `${cdnDomain}${featuredImage}`;
  }

  // Fallback if no CDN domain is configured
  return `https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=${fallbackText}`;
};

/**
 * Get fallback image URL for error handling
 * @param text - Text to display in placeholder
 * @returns Fallback image URL
 */
export const getFallbackImageUrl = (text: string = 'Image+Not+Found'): string => {
  return `https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=${text}`;
};

/**
 * Handle image error by setting fallback source
 * @param event - Image error event
 * @param fallbackText - Text for fallback image
 */
export const handleImageError = (
  event: React.SyntheticEvent<HTMLImageElement>, 
  fallbackText: string = 'Image+Not+Found'
): void => {
  const target = event.target as HTMLImageElement;
  target.src = getFallbackImageUrl(fallbackText);
};

/**
 * Get responsive image sizes for different breakpoints
 * @param variant - Image variant (featured, default, compact)
 * @returns Sizes string for responsive images
 */
export const getImageSizes = (variant: 'featured' | 'default' | 'compact' = 'default'): string => {
  switch (variant) {
    case 'featured':
      return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
    case 'compact':
      return '(max-width: 768px) 25vw, 10vw';
    default:
      return '(max-width: 768px) 50vw, 25vw';
  }
};

/**
 * Optimize image URL with width and quality parameters
 * @param imageUrl - Original image URL
 * @param width - Desired width
 * @param quality - Image quality (1-100)
 * @returns Optimized image URL
 */
export const optimizeImageUrl = (
  imageUrl: string, 
  width?: number, 
  quality: number = 80
): string => {
  // Skip optimization for placeholder images
  if (imageUrl.includes('placeholder.com')) {
    return imageUrl;
  }

  // Skip optimization for external URLs (not from our CDN)
  const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
  if (cdnDomain && !imageUrl.startsWith(cdnDomain)) {
    return imageUrl;
  }

  // Add optimization parameters if supported
  const url = new URL(imageUrl);
  if (width) {
    url.searchParams.set('w', width.toString());
  }
  url.searchParams.set('q', quality.toString());
  
  return url.toString();
};
