// Home Page V1 - Initial Implementation
// This component represents the main home page layout and structure

import React from 'react';
import HeroSection from "@/features/home/<USER>/hero";
import { BreakingNews, UpcomingFixtures } from "@/features/home/<USER>/content-sections";
import { EngagementFeatures } from "@/features/home/<USER>/engagement-features";

/**
 * Home Page V1 - Initial Implementation
 * 
 * @version 1.0.0
 * @since 2024-01-25
 * 
 * @description
 * Main home page component that combines all major sections:
 * - Hero Section with Live Scores
 * - Upcoming Fixtures (priority section)
 * - Engagement Features
 * - Breaking News
 * 
 * @example
 * ```tsx
 * import { HomePage } from '@/features/home/<USER>';
 * 
 * export default function HomeRoute() {
 *   return <HomePage />;
 * }
 * ```
 */
export default function HomePageV1() {
  return (
    <div className="min-h-screen">
      {/* Hero Section with Live Scores */}
      <HeroSection />

      {/* Content Sections */}
      <main>
        {/* Upcoming Fixtures Section - Moved to top priority */}
        <UpcomingFixtures />

        {/* Engagement Features Section */}
        <EngagementFeatures />

        {/* Breaking News Section */}
        <BreakingNews />
      </main>
    </div>
  );
}
