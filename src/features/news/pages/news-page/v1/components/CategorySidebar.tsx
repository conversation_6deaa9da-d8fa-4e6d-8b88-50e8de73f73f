'use client';

import React from 'react';
import type { NewsCategory } from '@/lib/api/newsCategories';
import { mergeCategories, formatCategoryDisplayName } from '@/features/news/utils/categoryUtils';

interface CategorySidebarProps {
  categories: NewsCategory[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

/**
 * Category Sidebar Component
 * 
 * Displays a list of news categories with icons and counts.
 * Allows users to filter news by category.
 */
export const CategorySidebar: React.FC<CategorySidebarProps> = ({
  categories,
  selectedCategory,
  onCategoryChange
}) => {
  // Use shared utility to merge categories
  const allCategories = mergeCategories(categories);





  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>

      <div className="space-y-2">
        {allCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.slug)}
            className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-colors duration-200 ${selectedCategory === category.slug
              ? 'bg-blue-50 text-blue-700 border border-blue-200'
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }`}
          >
            <div className="flex items-center space-x-3">
              <span className="text-lg">{category.icon}</span>
              <span className="font-medium">
                {formatCategoryDisplayName(category, false)}
              </span>
            </div>

            {category.count !== undefined && (
              <span className={`text-xs px-2 py-1 rounded-full ${selectedCategory === category.slug
                ? 'bg-blue-100 text-blue-600'
                : 'bg-gray-100 text-gray-500'
                }`}>
                {category.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Category Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          <div className="flex justify-between">
            <span>Total Articles</span>
            <span className="font-medium">
              {categories.reduce((sum, cat) => sum + (cat.count || 0), 0) || '---'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
