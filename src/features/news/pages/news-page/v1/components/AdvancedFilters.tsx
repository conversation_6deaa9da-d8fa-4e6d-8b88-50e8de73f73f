'use client';

import React, { useState } from 'react';

interface AdvancedFiltersProps {
  onFiltersChange: (filters: {
    dateRange?: { from: string; to: string };
    sortBy?: 'latest' | 'popular' | 'trending';
    featuredOnly?: boolean;
  }) => void;
  className?: string;
}

/**
 * Advanced Filters Component
 * 
 * Provides additional filtering options like date range,
 * sorting, and featured articles toggle.
 */
export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  onFiltersChange,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'trending'>('latest');
  const [featuredOnly, setFeaturedOnly] = useState(false);
  const [dateRange, setDateRange] = useState<{ from: string; to: string }>({
    from: '',
    to: ''
  });

  const handleSortChange = (newSort: 'latest' | 'popular' | 'trending') => {
    setSortBy(newSort);
    onFiltersChange({
      sortBy: newSort,
      featuredOnly,
      dateRange: dateRange.from && dateRange.to ? dateRange : undefined
    });
  };

  const handleFeaturedToggle = () => {
    const newFeaturedOnly = !featuredOnly;
    setFeaturedOnly(newFeaturedOnly);
    onFiltersChange({
      sortBy,
      featuredOnly: newFeaturedOnly,
      dateRange: dateRange.from && dateRange.to ? dateRange : undefined
    });
  };

  const handleDateRangeChange = (field: 'from' | 'to', value: string) => {
    const newDateRange = { ...dateRange, [field]: value };
    setDateRange(newDateRange);
    
    if (newDateRange.from && newDateRange.to) {
      onFiltersChange({
        sortBy,
        featuredOnly,
        dateRange: newDateRange
      });
    }
  };

  const clearFilters = () => {
    setSortBy('latest');
    setFeaturedOnly(false);
    setDateRange({ from: '', to: '' });
    onFiltersChange({});
  };

  const hasActiveFilters = sortBy !== 'latest' || featuredOnly || dateRange.from || dateRange.to;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
      >
        <div className="flex items-center space-x-2">
          <svg
            className="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
            />
          </svg>
          <span className="font-medium text-gray-900">Advanced Filters</span>
          {hasActiveFilters && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        
        <svg
          className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
            isExpanded ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="px-4 pb-4 border-t border-gray-200 space-y-4">
          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'latest', label: 'Latest', icon: '🕒' },
                { value: 'popular', label: 'Popular', icon: '🔥' },
                { value: 'trending', label: 'Trending', icon: '📈' }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleSortChange(option.value as any)}
                  className={`px-3 py-2 text-sm rounded-lg border transition-colors duration-200 ${
                    sortBy === option.value
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <span className="mr-1">{option.icon}</span>
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Only Toggle */}
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={featuredOnly}
                onChange={handleFeaturedToggle}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                Featured articles only
              </span>
            </label>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">From</label>
                <input
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => handleDateRangeChange('from', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">To</label>
                <input
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => handleDateRangeChange('to', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="pt-2 border-t border-gray-200">
              <button
                onClick={clearFilters}
                className="w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
