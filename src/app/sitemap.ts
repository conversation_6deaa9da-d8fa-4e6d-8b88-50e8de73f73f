import { MetadataRoute } from 'next';

// Mock function to fetch all news articles - replace with real API call
async function getAllNewsArticles() {
  // This would be replaced with actual API call
  // For now, return mock data
  return [
    {
      slug: 'manchester-united-signs-mbappe-record-deal',
      publishedAt: '2024-01-20T10:00:00Z',
      updatedAt: '2024-01-20T10:00:00Z'
    },
    {
      slug: 'liverpool-defeats-arsenal-premier-league',
      publishedAt: '2024-01-20T14:00:00Z',
      updatedAt: '2024-01-20T14:00:00Z'
    },
    {
      slug: 'champions-league-draw-exciting-matchups',
      publishedAt: '2024-01-19T16:00:00Z',
      updatedAt: '2024-01-19T16:00:00Z'
    },
    {
      slug: 'ronaldo-exclusive-interview-future-plans',
      publishedAt: '2024-01-19T12:00:00Z',
      updatedAt: '2024-01-19T12:00:00Z'
    },
    {
      slug: 'world-cup-2026-venues-announced',
      publishedAt: '2024-01-18T09:00:00Z',
      updatedAt: '2024-01-18T09:00:00Z'
    }
  ];
}

// Mock function to fetch all news categories
async function getAllNewsCategories() {
  return [
    { slug: 'breaking', updatedAt: '2024-01-20T00:00:00Z' },
    { slug: 'transfer', updatedAt: '2024-01-20T00:00:00Z' },
    { slug: 'match', updatedAt: '2024-01-20T00:00:00Z' },
    { slug: 'interview', updatedAt: '2024-01-19T00:00:00Z' },
    { slug: 'league', updatedAt: '2024-01-19T00:00:00Z' },
    { slug: 'general', updatedAt: '2024-01-18T00:00:00Z' }
  ];
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000';

  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/news`,
      lastModified: new Date(),
      changeFrequency: 'hourly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/fixtures`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/leagues`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
  ];

  // News articles
  const articles = await getAllNewsArticles();
  const newsPages: MetadataRoute.Sitemap = articles.map((article) => ({
    url: `${baseUrl}/news/${article.slug}`,
    lastModified: new Date(article.updatedAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // News categories
  const categories = await getAllNewsCategories();
  const categoryPages: MetadataRoute.Sitemap = categories.map((category) => ({
    url: `${baseUrl}/news?category=${category.slug}`,
    lastModified: new Date(category.updatedAt),
    changeFrequency: 'daily' as const,
    priority: 0.6,
  }));

  // News pagination pages (example for first 10 pages)
  const paginationPages: MetadataRoute.Sitemap = Array.from({ length: 10 }, (_, i) => ({
    url: `${baseUrl}/news?page=${i + 1}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: i === 0 ? 0.9 : 0.5, // First page has higher priority
  }));

  return [
    ...staticPages,
    ...newsPages,
    ...categoryPages,
    ...paginationPages,
  ];
}
