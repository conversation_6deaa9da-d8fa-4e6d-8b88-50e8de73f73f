'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import NewsLayout from '../../../layouts/NewsLayout';
import { SocialShare } from '../../news-page/v1/components/SocialShare';
import { NewsService } from '../../../services/NewsService';
import { useReadingAnalytics } from '../../news-page/v1/hooks/useReadingAnalytics';
import { getImageUrl } from '@/features/news/utils/imageUtils';
import '../v1/styles/article-content.css';

// Types
interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string | null;
  publishedAt: string;
  updatedAt: string;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isFeatured: boolean;
  priority: number;
  status: string;
  tags: string[];
  category: {
    id: number;
    slug: string;
    name: string;
    description: string;
    icon: string;
    color: string;
  };
  authorId: number;
  createdAt: string;
  metaTitle?: string;
  metaDescription?: string;
  relatedLeagueId?: number;
  relatedTeamId?: number;
  relatedPlayerId?: number;
  relatedFixtureId?: number;
}

interface RelatedArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: string | null;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  readTime: number;
  publishedAt: string;
}

interface NewsDetailPageV2Props {
  article: Article;
}

/**
 * News Detail Page V2 - Restructured Layout
 * 
 * @version 2.0.0
 * @since 2024-01-25
 * 
 * @description
 * Restructured news detail page with shared layout:
 * - Left: Category sidebar (consistent with news list)
 * - Right: Article content (optimized display)
 * - Shared layout component for consistency
 * - Enhanced performance and SEO
 * 
 * @features
 * - Shared layout with news list page
 * - Category navigation consistency
 * - Reading analytics tracking
 * - Social sharing integration
 * - Related articles
 * - SEO optimized
 * - Performance optimized
 */
export default function NewsDetailPageV2({ article }: NewsDetailPageV2Props) {
  const router = useRouter();
  const [relatedArticles, setRelatedArticles] = useState<RelatedArticle[]>([]);
  const [isLoadingRelated, setIsLoadingRelated] = useState(false);
  const [timeAgo, setTimeAgo] = useState<string>('');
  const [isClient, setIsClient] = useState(false);

  // Memoized service instance
  const newsService = React.useMemo(() => NewsService.getInstance(), []);

  // Reading analytics
  const { trackView, trackShare, trackReadingTime } = useReadingAnalytics({
    articleId: article.id.toString(),
    articleSlug: article.slug,
    categoryId: article.category.id.toString()
  });

  // Set client-side flag
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Update time ago periodically
  useEffect(() => {
    if (!isClient) return;

    const updateTimeAgo = () => {
      try {
        const time = formatDistanceToNow(new Date(article.publishedAt), { addSuffix: true });
        setTimeAgo(time);
      } catch (error) {
        console.warn('Error formatting time:', error);
        setTimeAgo('Recently');
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [article.publishedAt, isClient]);

  // Track article view
  useEffect(() => {
    if (isClient) {
      trackView();

      // Track reading time when component unmounts
      return () => {
        trackReadingTime();
      };
    }
  }, [isClient, trackView, trackReadingTime]);

  // Fetch related articles
  useEffect(() => {
    const fetchRelatedArticles = async () => {
      setIsLoadingRelated(true);
      try {
        console.log('🔄 Fetching related articles for:', article.slug);
        const articles = await newsService.getRelatedArticles(article.slug, 4);

        const relatedArticles: RelatedArticle[] = articles.map(article => ({
          id: article.id,
          title: article.title,
          slug: article.slug,
          excerpt: article.excerpt,
          featuredImage: article.featuredImage,
          category: {
            id: article.category.id,
            name: article.category.name,
            slug: article.category.slug
          },
          readTime: 5, // Default read time
          publishedAt: article.publishedAt
        }));

        setRelatedArticles(relatedArticles);
        console.log('✅ Related articles loaded:', relatedArticles.length);
      } catch (error) {
        console.error('❌ Error fetching related articles:', error);
        setRelatedArticles([]);
      } finally {
        setIsLoadingRelated(false);
      }
    };

    fetchRelatedArticles();
  }, [article.slug, newsService]);

  // Handle category change (navigate to news page with new URL structure)
  const handleCategoryChange = useCallback((category: string) => {
    console.log('📂 Navigating to category:', category);
    const url = category === 'all' ? '/news' : `/news-category/${category}`;
    router.push(url);
  }, [router]);

  // Handle share
  const handleShare = useCallback(async (platform: string) => {
    console.log('📤 Sharing article on:', platform);
    await trackShare(platform);

    // Increment share count in backend
    try {
      await newsService.incrementArticleShareCount(article.id.toString());
    } catch (error) {
      console.warn('Failed to increment share count:', error);
    }
  }, [trackShare, newsService, article.id]);

  // Generate article image URL
  const getArticleImageUrl = (imagePath: string | null, isLarge: boolean = false): string => {
    if (!imagePath) {
      const size = isLarge ? '800x450' : '400x225';
      const fallbackText = encodeURIComponent('News Image');
      return `https://via.placeholder.com/${size}/f3f4f6/9ca3af?text=${fallbackText}`;
    }

    const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
    if (!cdnDomain) {
      const size = isLarge ? '800x450' : '400x225';
      const fallbackText = encodeURIComponent('News Image');
      return `https://via.placeholder.com/${size}/f3f4f6/9ca3af?text=${fallbackText}`;
    }

    const cleanPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
    return `${cdnDomain}${cleanPath}`;
  };

  const imageUrl = getArticleImageUrl(article.featuredImage, true);
  const fallbackImageUrl = 'https://via.placeholder.com/800x450/f3f4f6/9ca3af?text=News+Image';
  const articleUrl = `${process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'}/news/${article.slug}`;

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    "headline": article.title,
    "description": article.excerpt,
    "image": imageUrl,
    "datePublished": article.publishedAt,
    "dateModified": article.updatedAt,
    "author": {
      "@type": "Organization",
      "name": "Sports News Team"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Sports News Platform",
      "logo": {
        "@type": "ImageObject",
        "url": `${process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'}/images/logo.png`
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": articleUrl
    },
    "articleSection": article.category.name,
    "keywords": article.tags.join(", ")
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      {/* Main Layout */}
      <NewsLayout
        selectedCategory={article.category.slug}
        onCategoryChange={handleCategoryChange}
        showSearch={false} // Hide search on detail page for cleaner look
        className="news-detail-page"
      >
        {/* Article Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6 lg:p-8">
            {/* Breadcrumb Navigation */}
            <nav className="mb-6" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm text-gray-500">
                <li>
                  <Link href="/" className="hover:text-gray-700 transition-colors">
                    Home
                  </Link>
                </li>
                <li>/</li>
                <li>
                  <Link href="/news" className="hover:text-gray-700 transition-colors">
                    News
                  </Link>
                </li>
                <li>/</li>
                <li>
                  <Link
                    href={`/news-category/${article.category.slug}`}
                    className="hover:text-gray-700 transition-colors"
                  >
                    {article.category.name}
                  </Link>
                </li>
                <li>/</li>
                <li className="text-gray-900 font-medium truncate">
                  {article.title}
                </li>
              </ol>
            </nav>

            {/* Article Header */}
            <header className="mb-8">
              {/* Category Badge */}
              <div className="mb-4">
                <Link
                  href={`/news-category/${article.category.slug}`}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors"
                  style={{
                    backgroundColor: `${article.category.color}20`,
                    color: article.category.color
                  }}
                >
                  <span className="mr-1">{article.category.icon}</span>
                  {article.category.name}
                </Link>
              </div>

              {/* Title */}
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight mb-4">
                {article.title}
              </h1>

              {/* Excerpt */}
              <p className="text-xl text-gray-600 leading-relaxed mb-6">
                {article.excerpt}
              </p>

              {/* Meta Information */}
              <div className="flex items-center justify-between flex-wrap gap-4 text-sm text-gray-500">
                <div className="flex items-center space-x-4">
                  <span>Published {timeAgo}</span>
                  <span>•</span>
                  <span>{article.viewCount.toLocaleString()} views</span>
                  <span>•</span>
                  <span>5 min read</span>
                </div>

                <SocialShare
                  url={articleUrl}
                  title={article.title}
                  description={article.excerpt}
                  variant="horizontal"
                  onShare={handleShare}
                />
              </div>
            </header>

            {/* Featured Image */}
            <div className="mb-8">
              <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-200">
                <img
                  src={imageUrl}
                  alt={article.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = fallbackImageUrl;
                  }}
                />
              </div>
            </div>

            {/* Article Content */}
            <article className="prose prose-lg max-w-none overflow-hidden">
              <div className="text-gray-900 leading-relaxed">
                <div
                  className="article-content overflow-hidden"
                  dangerouslySetInnerHTML={{ __html: article.content }}
                />
              </div>
            </article>

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-200">
              {/* Tags */}
              {article.tags && article.tags.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {article.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Share */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Share this article
                </div>
                <SocialShare
                  url={articleUrl}
                  title={article.title}
                  description={article.excerpt}
                  variant="horizontal"
                  onShare={handleShare}
                />
              </div>
            </footer>
          </div>

          {/* Related Articles */}
          {relatedArticles.length > 0 && (
            <div className="border-t border-gray-200 bg-gray-50 p-6 lg:p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Related Articles</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {relatedArticles.map((relatedArticle) => (
                  <Link
                    key={relatedArticle.id}
                    href={`/news/${relatedArticle.slug}`}
                    className="group block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <div className="aspect-video bg-gray-200 overflow-hidden">
                      <img
                        src={getArticleImageUrl(relatedArticle.featuredImage)}
                        alt={relatedArticle.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=News+Image';
                        }}
                      />
                    </div>
                    <div className="p-4">
                      <div className="text-xs text-gray-500 mb-2">
                        {relatedArticle.category.name}
                      </div>
                      <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                        {relatedArticle.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                        {relatedArticle.excerpt}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </NewsLayout>
    </>
  );
}
