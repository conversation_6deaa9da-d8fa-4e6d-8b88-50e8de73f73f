'use client';

import React, { useState } from 'react';
import { CategorySidebar } from './components/CategorySidebar';
import { NewsList } from './components/NewsList';
import { SearchBar } from './components/SearchBar';
import { AdvancedFilters } from './components/AdvancedFilters';
import { StructuredData } from './components/StructuredData';
import { Pagination } from './components/Pagination';
import { useNews } from './hooks/useNews';
import { useInfiniteScroll } from './hooks/useInfiniteScroll';

/**
 * News Page V1 - 2-Column Layout
 * 
 * @version 1.0.0
 * @since 2024-01-25
 * 
 * @description
 * Main news page with 2-column layout:
 * - Left: Category sidebar with filters
 * - Right: News list with articles
 * - Mobile: Stacked layout with dropdown categories
 * 
 * @example
 * ```tsx
 * import { NewsPage } from '@/features/news/pages';
 * 
 * export default function NewsRoute() {
 *   return <NewsPage />;
 * }
 * ```
 */
export default function NewsPageV1() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [advancedFilters, setAdvancedFilters] = useState<{
    dateRange?: { from: string; to: string };
    sortBy?: 'latest' | 'popular' | 'trending';
    featuredOnly?: boolean;
  }>({});
  const [paginationMode, setPaginationMode] = useState<'infinite' | 'numbers'>('numbers');

  const {
    articles,
    categories,
    isLoading,
    error,
    hasMore,
    currentPage,
    totalPages,
    totalItems,
    loadMore,
    goToPage
  } = useNews({
    category: selectedCategory === 'all' ? undefined : selectedCategory,
    search: searchQuery || undefined,
    limit: 20,
    enableCache: true,
    autoFetch: true,
    ...advancedFilters
  });

  // Infinite scroll hook (only used when in infinite mode)
  const { observerRef } = useInfiniteScroll({
    hasMore: paginationMode === 'infinite' ? hasMore : false,
    isLoading,
    onLoadMore: loadMore,
    threshold: 300
  });

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleSearchSubmit = (query: string) => {
    setSearchQuery(query);
  };

  const handleAdvancedFiltersChange = (filters: typeof advancedFilters) => {
    setAdvancedFilters(filters);
  };

  const handlePageChange = (page: number) => {
    goToPage(page);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const togglePaginationMode = () => {
    setPaginationMode(prev => prev === 'infinite' ? 'numbers' : 'infinite');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* SEO Structured Data */}
      <StructuredData
        articles={articles}
        currentPage={currentPage}
        totalPages={totalPages}
      />

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">News</h1>
              <p className="text-gray-600 mt-1">Latest football news and updates</p>
              {totalItems > 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  Page {currentPage} of {totalPages} • {totalItems} articles
                </p>
              )}
            </div>

            {/* Mobile Search */}
            <div className="md:hidden">
              <SearchBar
                value={searchQuery}
                onChange={handleSearchChange}
                onSubmit={handleSearchSubmit}
                placeholder="Search news..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <CategorySidebar
                categories={categories}
                selectedCategory={selectedCategory}
                onCategoryChange={handleCategoryChange}
              />

              {/* Desktop Search */}
              <div className="hidden md:block mt-6 pt-6 border-t border-gray-200">
                <SearchBar
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onSubmit={handleSearchSubmit}
                  placeholder="Search news..."
                />
              </div>

              {/* Advanced Filters */}
              <div className="mt-4">
                <AdvancedFilters
                  onFiltersChange={handleAdvancedFiltersChange}
                />
              </div>

              {/* Pagination Mode Toggle */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={togglePaginationMode}
                  className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Switch to {paginationMode === 'infinite' ? 'Number' : 'Infinite'} Pagination
                </button>
              </div>
            </div>
          </div>

          {/* Right Content - News List */}
          <div className="lg:col-span-3">
            <NewsList
              articles={articles}
              isLoading={isLoading}
              error={error}
              hasMore={paginationMode === 'infinite' ? hasMore : false}
              onLoadMore={loadMore}
              selectedCategory={selectedCategory}
              searchQuery={searchQuery}
            />

            {/* Pagination - Number-based */}
            {paginationMode === 'numbers' && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                onPageChange={handlePageChange}
                isLoading={isLoading}
                showInfo={true}
                maxVisiblePages={7}
              />
            )}

            {/* Infinite Scroll Observer - Only for infinite mode */}
            {paginationMode === 'infinite' && (
              <div ref={observerRef} className="h-4" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
