'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { fetchNews, type NewsArticle, type NewsAPIParams } from '@/lib/api/news';
import { fetchNewsCategories, type NewsCategory } from '@/lib/api/newsCategories';
import { NewsService } from '../../../../services/NewsService';

interface UseNewsParams {
  category?: string;
  search?: string;
  limit?: number;
  page?: number;
  initialData?: NewsArticle[];
  autoFetch?: boolean;
  enableCache?: boolean;
}

interface UseNewsReturn {
  articles: NewsArticle[];
  categories: NewsCategory[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  loadMore: () => void;
  refresh: () => void;
  goToPage: (page: number) => void;
  clearCache: () => void;
  getCacheStats: () => any;
}

/**
 * Optimized News Hook - Consolidated version with caching and performance improvements
 *
 * Features:
 * - Automatic caching with TTL
 * - Optimized re-renders with memoization
 * - Separate loading states for better UX
 * - Error handling with fallbacks
 * - Cache management utilities
 * - Server-side pagination support
 */
export const useNews = ({
  category,
  search,
  limit = 20,
  page = 1,
  initialData = [],
  autoFetch = true,
  enableCache = true
}: UseNewsParams): UseNewsReturn => {
  const [articles, setArticles] = useState<NewsArticle[]>(initialData);
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(page);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Memoized service instance
  const newsService = useMemo(() => NewsService.getInstance(), []);

  // Optimized fetch articles with caching and loading states
  const fetchArticles = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      const params: NewsAPIParams = {
        page,
        limit, // Use exact limit - server handles pagination
        ...(category && category !== 'all' && { category }), // Send category to server
        ...(search && { search })
      };

      console.log('🔄 Fetching articles with server-side pagination:', params);

      // Use service with caching if enabled, otherwise direct API call
      let response;
      if (enableCache) {
        response = await newsService.getArticles(params);
      } else {
        response = await fetchNews(params);
      }

      console.log('✅ Server response:', {
        articlesCount: response.data.length,
        currentPage: response.meta.currentPage,
        totalPages: response.meta.totalPages,
        totalItems: response.meta.totalItems
      });

      // Server handles all filtering and pagination
      if (append) {
        setArticles(prev => [...prev, ...response.data]);
      } else {
        setArticles(response.data);
      }

      setCurrentPage(response.meta.currentPage);
      setTotalPages(response.meta.totalPages);
      setTotalItems(response.meta.totalItems);
      setHasMore(response.meta.hasNextPage || response.meta.currentPage < response.meta.totalPages);

    } catch (err) {
      console.error('Error fetching news:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch news');
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [category, search, limit, enableCache, newsService]);

  // Load more articles (pagination)
  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      fetchArticles(currentPage + 1, true);
    }
  }, [fetchArticles, currentPage, hasMore, isLoadingMore]);

  // Refresh articles
  const refresh = useCallback(() => {
    setCurrentPage(1);
    fetchArticles(1, false);
  }, [fetchArticles]);

  // Go to specific page
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
      fetchArticles(page, false);
    }
  }, [fetchArticles, totalPages, currentPage]);

  // Cache management functions
  const clearCache = useCallback(() => {
    if (enableCache) {
      newsService.clearCache();
    }
  }, [enableCache, newsService]);

  const getCacheStats = useCallback(() => {
    if (enableCache) {
      return newsService.getCacheStats();
    }
    return null;
  }, [enableCache, newsService]);

  // Load categories from service with caching
  useEffect(() => {
    const loadCategories = async () => {
      try {
        console.log('📂 Loading news categories via service');
        let response;
        if (enableCache) {
          response = await newsService.getCategories();
        } else {
          response = await fetchNewsCategories();
        }
        setCategories(response.data);
        console.log('✅ Categories loaded via service:', response.data.length);
      } catch (error) {
        console.error('❌ Failed to load categories:', error);
        // Fallback to empty array - CategorySidebar will use defaults
        setCategories([]);
      }
    };

    if (autoFetch) {
      loadCategories();
    }
  }, [newsService, enableCache, autoFetch]);

  // Effect to fetch articles when dependencies change
  useEffect(() => {
    if (autoFetch) {
      setCurrentPage(1);
      fetchArticles(1, false);
    }
  }, [category, search, autoFetch, fetchArticles]);

  // Initial load
  useEffect(() => {
    if (autoFetch && initialData.length === 0) {
      fetchArticles(1, false);
    }
  }, [autoFetch, initialData.length, fetchArticles]);

  return {
    articles,
    categories,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    currentPage,
    totalPages,
    totalItems,
    loadMore,
    refresh,
    goToPage,
    clearCache,
    getCacheStats
  };
};
