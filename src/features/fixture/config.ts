// Fixture Feature Configuration
// This file manages all version configurations for the fixture feature

export const fixtureFeatureConfig = {
  // Page-level configurations
  pages: {
    fixturePage: 'v1',        // Main fixture listing page
    fixtureDetail: 'v1',      // Individual fixture detail page
  },
  
  // Component-level configurations
  components: {
    fixtureCard: 'v1',        // Fixture card component
    fixtureList: 'v1',        // Fixture list component
    leagueFilter: 'v1',       // League filter component
    dateFilter: 'v1',         // Date filter component
    liveIndicator: 'v1',      // Live match indicator
    matchStats: 'v1',         // Match statistics component
  }
} as const;

export type FixtureFeatureConfig = typeof fixtureFeatureConfig;

// Environment-based configurations
export const FIXTURE_FEATURE_VERSIONS = {
  development: {
    ...fixtureFeatureConfig,
  },
  production: {
    ...fixtureFeatureConfig,
  }
} as const;

// Get current configuration based on environment
export const getCurrentFixtureConfig = () => {
  const env = process.env.NODE_ENV as keyof typeof FIXTURE_FEATURE_VERSIONS;
  return FIXTURE_FEATURE_VERSIONS[env] || FIXTURE_FEATURE_VERSIONS.production;
};

// Version metadata for documentation
export const FIXTURE_VERSION_INFO = {
  fixturePage: {
    v1: { name: 'Fixture Page V1', status: 'development', releaseDate: '2024-01-25' },
  },
  fixtureDetail: {
    v1: { name: 'Fixture Detail V1', status: 'development', releaseDate: '2024-01-25' },
  }
} as const;
