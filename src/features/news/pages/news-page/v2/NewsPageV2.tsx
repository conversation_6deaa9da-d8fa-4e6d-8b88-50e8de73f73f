'use client';

import React, { useState, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { NewsLayout } from '../../../layouts/NewsLayout';
import { NewsList } from '../v1/components/NewsList';
import { Pagination } from '../v1/components/Pagination';
import { StructuredData } from '../v1/components/StructuredData';
import { useNews } from '../v1/hooks/useNews';

interface NewsPageV2Props {
  initialCategory?: string;
  initialSearchParams?: { [key: string]: string | string[] | undefined };
}

/**
 * News Page V2 - Restructured Layout
 *
 * @version 2.0.0
 * @since 2024-01-25
 *
 * @description
 * Restructured news page with optimized layout:
 * - Shared layout component for consistency
 * - Left: Category sidebar (sticky)
 * - Right: News list with number pagination (10 articles per page)
 * - Category-based filtering via dedicated routes
 * - High performance with memoization
 * - URL-based state management
 *
 * @features
 * - 10 articles per page with number pagination
 * - Category filtering via dedicated routes (/news-category/{category})
 * - Search functionality
 * - Responsive design
 * - SEO optimized
 * - Performance optimized
 *
 * @urlStructure
 * - /news - All articles
 * - /news?page=2 - All articles page 2
 * - /news-category/football - Football articles
 * - /news-category/football?page=2 - Football articles page 2
 * - /news/{slug} - Article detail
 */
export default function NewsPageV2({
  initialCategory,
  initialSearchParams
}: NewsPageV2Props = {}) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get URL parameters with initial values support
  const categoryFromUrl = initialCategory || searchParams.get('category') || 'all';
  const searchFromUrl = (initialSearchParams?.search as string) || searchParams.get('search') || '';
  const pageFromUrl = parseInt(
    (initialSearchParams?.page as string) || searchParams.get('page') || '1',
    10
  );

  // Local state
  const [selectedCategory, setSelectedCategory] = useState<string>(categoryFromUrl);
  const [searchQuery, setSearchQuery] = useState<string>(searchFromUrl);

  // News hook with optimized settings
  const {
    articles,
    categories,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalItems,
    goToPage
  } = useNews({
    category: selectedCategory === 'all' ? undefined : selectedCategory,
    search: searchQuery || undefined,
    page: pageFromUrl,
    limit: 10, // Fixed 10 articles per page
    enableCache: true,
    autoFetch: true
  });

  // Update URL when filters change - New URL structure
  const updateUrl = useCallback((newCategory: string, newSearch: string, newPage: number = 1) => {
    const params = new URLSearchParams();

    // Add search parameter if exists
    if (newSearch) {
      params.set('search', newSearch);
    }

    // Add page parameter if not page 1
    if (newPage > 1) {
      params.set('page', newPage.toString());
    }

    // Determine the base URL based on category
    let baseUrl: string;
    if (newCategory === 'all') {
      baseUrl = '/news';
    } else {
      baseUrl = `/news-category/${newCategory}`;
    }

    // Construct final URL
    const queryString = params.toString();
    const newUrl = queryString ? `${baseUrl}?${queryString}` : baseUrl;

    console.log('🔗 Navigating to:', newUrl);
    router.push(newUrl, { scroll: false });
  }, [router]);

  // Handle category change
  const handleCategoryChange = useCallback((category: string) => {
    console.log('📂 Category changed:', category);
    setSelectedCategory(category);
    updateUrl(category, searchQuery, 1); // Reset to page 1 when category changes
  }, [searchQuery, updateUrl]);

  // Handle search change
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle search submit
  const handleSearchSubmit = useCallback((query: string) => {
    console.log('🔍 Search submitted:', query);
    setSearchQuery(query);
    updateUrl(selectedCategory, query, 1); // Reset to page 1 when search changes
  }, [selectedCategory, updateUrl]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    console.log('📄 Page changed:', page);
    updateUrl(selectedCategory, searchQuery, page);
    goToPage(page);

    // Scroll to top of content
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [selectedCategory, searchQuery, updateUrl, goToPage]);

  // Generate page title based on filters
  const getPageTitle = () => {
    if (selectedCategory !== 'all' && categories.length > 0) {
      const category = categories.find(cat => cat.slug === selectedCategory);
      if (category) {
        return `${category.name} News`;
      }
    }

    if (searchQuery) {
      return `Search Results for "${searchQuery}"`;
    }

    return 'Latest Sports News';
  };

  // Generate page description
  const getPageDescription = () => {
    if (selectedCategory !== 'all' && categories.length > 0) {
      const category = categories.find(cat => cat.slug === selectedCategory);
      if (category) {
        return `Latest ${category.name.toLowerCase()} news and updates. Stay informed with breaking stories and analysis.`;
      }
    }

    if (searchQuery) {
      return `Search results for "${searchQuery}" in sports news. Find relevant articles and updates.`;
    }

    return 'Stay updated with the latest sports news, breaking stories, transfer updates, and match reports.';
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <StructuredData
        title={getPageTitle()}
        description={getPageDescription()}
        articles={articles}
        currentPage={currentPage}
        totalPages={totalPages}
      />

      {/* Main Layout */}
      <NewsLayout
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        showSearch={true}
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
      >
        {/* Content Area */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Content Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h2>
                <p className="mt-1 text-gray-600">{getPageDescription()}</p>
              </div>

              {/* Results Info */}
              {!isLoading && (
                <div className="text-sm text-gray-500">
                  {totalItems > 0 ? (
                    <>
                      Showing {((currentPage - 1) * 10) + 1}-{Math.min(currentPage * 10, totalItems)} of {totalItems} articles
                    </>
                  ) : (
                    'No articles found'
                  )}
                </div>
              )}
            </div>
          </div>

          {/* News List */}
          <div className="p-6">
            <NewsList
              articles={articles}
              isLoading={isLoading}
              error={error}
              hasMore={false} // Disable infinite scroll for pagination mode
              onLoadMore={() => { }} // Not used in pagination mode
              selectedCategory={selectedCategory}
              searchQuery={searchQuery}
              variant="grid" // Use grid layout for better organization
            />
          </div>

          {/* Pagination */}
          {!isLoading && totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                onPageChange={handlePageChange}
                isLoading={isLoading}
                showInfo={true}
                maxVisiblePages={7}
                className="justify-center"
              />
            </div>
          )}
        </div>
      </NewsLayout>
    </>
  );
}
