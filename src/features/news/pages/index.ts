// News Pages - Version Management
// This file manages all page-level components for the news feature

import { newsFeatureConfig } from '../config';

// Page exports based on configuration
export { default as NewsPage } from './news-page';
export { default as NewsDetailPage } from './news-detail-page';

// Dynamic imports for version switching
export const getNewsPage = () => {
  const version = newsFeatureConfig.pages.newsPage;
  return import(`./news-page/v${version}`).then(m => m.default);
};

// export const getNewsDetail = () => {
//   const version = newsFeatureConfig.pages.newsDetail;
//   return import(`./news-detail/v${version}`).then(m => m.default);
// };

// Version mapping for dynamic imports
export const NEWS_PAGE_VERSIONS = {
  v1: () => import('./news-page/v1').then(m => m.default),
  // v2: () => import('./news-page/v2').then(m => m.default),
} as const;

// export const NEWS_DETAIL_VERSIONS = {
//   v1: () => import('./news-detail/v1').then(m => m.default),
//   // v2: () => import('./news-detail/v2').then(m => m.default),
// } as const;

// Helper functions to get page versions
export const getNewsPageVersion = (version: keyof typeof NEWS_PAGE_VERSIONS = 'v1') => {
  return NEWS_PAGE_VERSIONS[version];
};

// export const getNewsDetailVersion = (version: keyof typeof NEWS_DETAIL_VERSIONS = 'v1') => {
//   return NEWS_DETAIL_VERSIONS[version];
// };
