// News Category Utilities - Shared utilities for category handling
// This module provides consistent category handling across news components

import type { NewsCategory } from '@/lib/api/newsCategories';

export interface CategoryConfig {
  id: string;
  name: string;
  slug: string;
  icon: string;
  color: string;
  count?: number;
  sortOrder?: number;
}

/**
 * Default categories configuration
 */
export const DEFAULT_CATEGORIES: CategoryConfig[] = [
  { id: 'all', name: 'All News', slug: 'all', icon: '📰', color: '#6B7280', sortOrder: 0 },
  { id: 'breaking', name: 'Breaking', slug: 'breaking', icon: '🔥', color: '#EF4444', sortOrder: 1 },
  { id: 'match', name: 'Match', slug: 'match', icon: '⚽', color: '#3B82F6', sortOrder: 2 },
  { id: 'transfer', name: 'Transfer', slug: 'transfer', icon: '🔄', color: '#F59E0B', sortOrder: 3 },
  { id: 'interview', name: 'Interview', slug: 'interview', icon: '🎤', color: '#8B5CF6', sortOrder: 4 },
  { id: 'league', name: 'League', slug: 'league', icon: '🏆', color: '#10B981', sortOrder: 5 },
  { id: 'general', name: 'General', slug: 'general', icon: '📝', color: '#6B7280', sortOrder: 6 }
];

/**
 * Icon mapping for category slugs
 */
export const CATEGORY_ICONS: Record<string, string> = {
  all: '📰',
  breaking: '🔥',
  match: '⚽',
  transfer: '🔄',
  interview: '🎤',
  league: '🏆',
  medical: '🏥',
  tactics: '📋',
  youth: '👶',
  womens: '👩',
  general: '📝'
};

/**
 * Color mapping for category slugs
 */
export const CATEGORY_COLORS: Record<string, string> = {
  all: 'bg-gray-100 text-gray-800',
  breaking: 'bg-red-100 text-red-800',
  match: 'bg-blue-100 text-blue-800',
  transfer: 'bg-yellow-100 text-yellow-800',
  interview: 'bg-purple-100 text-purple-800',
  league: 'bg-green-100 text-green-800',
  medical: 'bg-pink-100 text-pink-800',
  tactics: 'bg-indigo-100 text-indigo-800',
  youth: 'bg-orange-100 text-orange-800',
  womens: 'bg-rose-100 text-rose-800',
  general: 'bg-gray-100 text-gray-800'
};

/**
 * Get category icon by slug
 * @param slug - Category slug
 * @returns Category icon emoji
 */
export const getCategoryIcon = (slug: string): string => {
  return CATEGORY_ICONS[slug] || CATEGORY_ICONS.general;
};

/**
 * Get category color classes by slug
 * @param slug - Category slug
 * @returns Tailwind CSS color classes
 */
export const getCategoryColors = (slug: string): string => {
  return CATEGORY_COLORS[slug] || CATEGORY_COLORS.general;
};

/**
 * Merge API categories with default categories
 * @param apiCategories - Categories from API
 * @returns Merged categories list
 */
export const mergeCategories = (apiCategories: NewsCategory[]): CategoryConfig[] => {
  // Always include "All News" as first item
  const allCategory = DEFAULT_CATEGORIES[0];
  
  if (apiCategories.length === 0) {
    return DEFAULT_CATEGORIES;
  }

  // Convert API categories to CategoryConfig format
  const convertedCategories: CategoryConfig[] = apiCategories.map(cat => ({
    id: cat.id.toString(),
    name: cat.name,
    slug: cat.slug,
    icon: cat.icon || getCategoryIcon(cat.slug),
    color: cat.color,
    count: cat.count,
    sortOrder: cat.sortOrder || 999
  }));

  // Sort by sortOrder
  convertedCategories.sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));

  return [allCategory, ...convertedCategories];
};

/**
 * Filter categories by active status and article count
 * @param categories - Categories to filter
 * @param minArticleCount - Minimum article count (default: 0)
 * @returns Filtered categories
 */
export const filterActiveCategories = (
  categories: CategoryConfig[], 
  minArticleCount: number = 0
): CategoryConfig[] => {
  return categories.filter(cat => {
    // Always include "All News"
    if (cat.slug === 'all') {
      return true;
    }
    
    // Filter by article count if specified
    return (cat.count || 0) >= minArticleCount;
  });
};

/**
 * Get category by slug
 * @param categories - Categories list
 * @param slug - Category slug to find
 * @returns Category or null if not found
 */
export const getCategoryBySlug = (
  categories: CategoryConfig[], 
  slug: string
): CategoryConfig | null => {
  return categories.find(cat => cat.slug === slug) || null;
};

/**
 * Format category display name with count
 * @param category - Category object
 * @param showCount - Whether to show article count
 * @returns Formatted display name
 */
export const formatCategoryDisplayName = (
  category: CategoryConfig, 
  showCount: boolean = true
): string => {
  if (!showCount || !category.count) {
    return category.name;
  }
  
  return `${category.name} (${category.count})`;
};

/**
 * Check if category is special (breaking, featured, etc.)
 * @param slug - Category slug
 * @returns True if category is special
 */
export const isSpecialCategory = (slug: string): boolean => {
  const specialCategories = ['breaking', 'featured', 'trending'];
  return specialCategories.includes(slug);
};

/**
 * Get category priority for sorting
 * @param slug - Category slug
 * @returns Priority number (lower = higher priority)
 */
export const getCategoryPriority = (slug: string): number => {
  const priorities: Record<string, number> = {
    all: 0,
    breaking: 1,
    featured: 2,
    trending: 3,
    match: 4,
    transfer: 5,
    league: 6,
    interview: 7,
    general: 999
  };
  
  return priorities[slug] || 500;
};

/**
 * Sort categories by priority and name
 * @param categories - Categories to sort
 * @returns Sorted categories
 */
export const sortCategories = (categories: CategoryConfig[]): CategoryConfig[] => {
  return [...categories].sort((a, b) => {
    const priorityA = a.sortOrder ?? getCategoryPriority(a.slug);
    const priorityB = b.sortOrder ?? getCategoryPriority(b.slug);
    
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }
    
    return a.name.localeCompare(b.name);
  });
};
