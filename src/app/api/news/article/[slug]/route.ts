import { NextRequest, NextResponse } from 'next/server';

interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string | null;
  publishedAt: string;
  updatedAt: string;
  author: {
    name: string;
    bio: string;
    avatar?: string;
  };
  category: {
    id: number;
    name: string;
    slug: string;
  };
  tags: string[];
  readTime: number;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isFeatured: boolean;
  status: 'published' | 'draft' | 'archived';
}

interface NewsAPIResponse {
  success: boolean;
  article: NewsArticle | null;
  message?: string;
}

// Mock data for development - replace with real backend API calls
const mockArticles: Record<string, NewsArticle> = {
  'manchester-united-signs-mbappe-record-deal': {
    id: 1,
    title: 'Manchester United Signs Mbappe in Record Breaking Deal',
    slug: 'manchester-united-signs-mbappe-record-deal',
    excerpt: 'In a stunning move that has shocked the football world, Manchester United has secured the signature of <PERSON><PERSON><PERSON> in what is being called the transfer of the century.',
    content: `<p>In a stunning move that has shocked the football world, Manchester United has secured the signature of <PERSON><PERSON><PERSON> in what is being called the transfer of the century. The French superstar has agreed to a five-year deal worth a reported €200 million, making him the most expensive player in the club's history.</p>

<p>The 25-year-old forward, who has been the subject of intense speculation throughout the summer transfer window, chose Old Trafford over several other European giants including Real Madrid, Barcelona, and Paris Saint-Germain.</p>

<h3>Record-Breaking Deal</h3>

<p>The transfer fee of €200 million surpasses the previous record set by Neymar's move to PSG in 2017. United's willingness to break the bank for Mbappe demonstrates their commitment to returning to the pinnacle of European football.</p>

<blockquote>
<p>"Kylian is exactly the type of player we need to take this club to the next level. His pace, skill, and winning mentality will be invaluable as we compete for major trophies."</p>
<footer>— Erik ten Hag, Manchester United Manager</footer>
</blockquote>

<h3>What This Means for United</h3>

<p>Mbappe's arrival signals a new era of ambition at Old Trafford. The club has struggled to compete with Europe's elite in recent years, but this signing sends a clear message that they are serious about challenging for the Premier League title and Champions League.</p>

<ul>
<li>Immediate impact on the attacking line with pace and creativity</li>
<li>Commercial benefits with increased shirt sales and sponsorship deals</li>
<li>Attraction factor for other world-class players</li>
<li>Renewed optimism among the fanbase</li>
</ul>

<h3>Fan Reactions</h3>

<p>United fans have been ecstatic about the news, with social media exploding with celebrations. Season ticket sales have reportedly increased by 300% since the announcement, and the club shop has been overwhelmed with pre-orders for Mbappe jerseys.</p>

<p>The French international is expected to make his debut in the upcoming Premier League season opener against Arsenal at Old Trafford. Tickets for the match have already sold out, with secondary market prices reaching record highs.</p>`,
    featuredImage: '/images/news/mbappe-united.jpg',
    publishedAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-01-20T10:00:00Z',
    author: {
      name: 'John Smith',
      bio: 'Senior Sports Journalist with over 10 years of experience covering Premier League transfers and breaking news.',
      avatar: '/images/authors/john-smith.jpg'
    },
    category: {
      id: 1,
      name: 'Transfer News',
      slug: 'transfer'
    },
    tags: ['transfer', 'manchester-united', 'mbappe', 'premier-league', 'breaking-news'],
    readTime: 5,
    viewCount: 15420,
    shareCount: 892,
    likeCount: 3456,
    isFeatured: true,
    status: 'published'
  },
  'liverpool-defeats-arsenal-premier-league': {
    id: 2,
    title: 'Liverpool Defeats Arsenal 3-1 in Premier League Thriller',
    slug: 'liverpool-defeats-arsenal-premier-league',
    excerpt: 'Liverpool secured a crucial victory against Arsenal at Anfield, with goals from Salah, Mane, and Firmino sealing a comprehensive 3-1 win.',
    content: `<p>Liverpool secured a crucial victory against Arsenal at Anfield, with goals from Salah, Mane, and Firmino sealing a comprehensive 3-1 win in what was one of the most entertaining matches of the Premier League season.</p>

<p>The Reds dominated from the first whistle, with their high-pressing game causing problems for Arsenal's defense throughout the match. Jurgen Klopp's tactical masterclass was evident as his team controlled both possession and tempo.</p>

<h3>Match Highlights</h3>

<p>Mohamed Salah opened the scoring in the 23rd minute with a trademark curling effort from the edge of the box. The Egyptian's goal was his 15th of the season, maintaining his position as the league's top scorer.</p>

<p>Arsenal responded well and equalized through Gabriel Jesus just before halftime, but Liverpool's quality shone through in the second half.</p>

<h3>Second Half Dominance</h3>

<p>Sadio Mane restored Liverpool's lead with a brilliant header from Andy Robertson's cross, before Roberto Firmino sealed the victory with a late strike that showcased the Brazilian's technical ability.</p>

<blockquote>
<p>"The performance was outstanding. The way we pressed, the way we moved the ball, everything was perfect tonight."</p>
<footer>— Jurgen Klopp, Liverpool Manager</footer>
</blockquote>

<p>This victory moves Liverpool to within three points of the top four, keeping their Champions League hopes alive for another season.</p>`,
    featuredImage: '/images/news/liverpool-arsenal.jpg',
    publishedAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-01-20T14:00:00Z',
    author: {
      name: 'Sarah Johnson',
      bio: 'Football Correspondent specializing in Premier League match analysis and tactical breakdowns.',
      avatar: '/images/authors/sarah-johnson.jpg'
    },
    category: {
      id: 2,
      name: 'Match Reports',
      slug: 'match'
    },
    tags: ['match', 'liverpool', 'arsenal', 'premier-league', 'anfield'],
    readTime: 4,
    viewCount: 8930,
    shareCount: 445,
    likeCount: 1876,
    isFeatured: false,
    status: 'published'
  },
  'champions-league-draw-exciting-matchups': {
    id: 3,
    title: 'Champions League Draw: Exciting Matchups Revealed',
    slug: 'champions-league-draw-exciting-matchups',
    excerpt: 'The Champions League group stage draw has produced some thrilling matchups, with several mouth-watering clashes set to light up European football.',
    content: `<p>The Champions League group stage draw has produced some thrilling matchups, with several mouth-watering clashes set to light up European football this season.</p>

<p>The ceremony in Monaco delivered drama and excitement as Europe's elite clubs learned their fate for the upcoming campaign.</p>

<h3>Group of Death</h3>

<p>Group C has been labeled the "Group of Death" featuring Barcelona, Bayern Munich, Inter Milan, and Viktoria Plzen. This group promises to deliver some of the most competitive matches of the tournament.</p>

<p>Barcelona and Bayern Munich's reunion is particularly intriguing, given their recent history in the competition.</p>

<h3>English Clubs' Fate</h3>

<p>Manchester City received a relatively favorable draw, being placed alongside Sevilla, Borussia Dortmund, and Copenhagen in Group G.</p>

<p>Liverpool will face a tough test against Napoli, Ajax, and Rangers in Group A, while Chelsea must navigate past AC Milan, Salzburg, and Dinamo Zagreb in Group E.</p>

<p>The group stage matches will begin in September, with the final taking place at the Ataturk Olympic Stadium in Istanbul.</p>`,
    featuredImage: '/images/news/champions-league-draw.jpg',
    publishedAt: '2024-01-19T16:00:00Z',
    updatedAt: '2024-01-19T16:00:00Z',
    author: {
      name: 'Michael Rodriguez',
      bio: 'European Football Expert covering Champions League and international competitions.',
      avatar: '/images/authors/michael-rodriguez.jpg'
    },
    category: {
      id: 3,
      name: 'Champions League',
      slug: 'champions-league'
    },
    tags: ['champions-league', 'draw', 'european-football', 'group-stage'],
    readTime: 3,
    viewCount: 12450,
    shareCount: 678,
    likeCount: 2341,
    isFeatured: true,
    status: 'published'
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    console.log(`🔌 News Article API request - Slug: ${slug}`);

    // Try multiple backend endpoints for article detail - Updated to match new API structure
    const backendUrl = process.env.API_BASE_URL || process.env.BASE_URL || 'http://localhost:3000';
    console.log(`🔗 Using backend URL: ${backendUrl}`);
    const endpoints = [
      `${backendUrl}/news/${slug}`,                    // Primary endpoint: GET /news/{slug}
      `${backendUrl}/public/news/${slug}`,             // Public endpoint: public/news/{slug}
      `${backendUrl}/news/article/${slug}`,            // Legacy: news/article/{slug}
      `${backendUrl}/news/articles/${slug}`,           // Legacy: news/articles/{slug}
      `${backendUrl}/public/news/article/${slug}`,     // Legacy public alternative
      `${backendUrl}/public/news/articles/${slug}`     // Legacy public alternative
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔌 Trying backend API: ${endpoint}`);

        const backendResponse = await fetch(endpoint, {
          headers: {
            'Content-Type': 'application/json',
          },
          next: { revalidate: 300 }, // Cache for 5 minutes
          signal: AbortSignal.timeout(10000) // 10 seconds timeout
        });

        console.log(`📡 Response status: ${backendResponse.status} for ${endpoint}`);

        if (backendResponse.ok) {
          const backendData = await backendResponse.json();
          console.log('✅ Backend API successful for article:', backendData);

          // Handle different response formats
          let article = null;
          if (backendData.article) {
            article = backendData.article;
          } else if (backendData.data) {
            article = backendData.data;
          } else if (backendData.id) {
            article = backendData;
          }

          if (article) {
            console.log('✅ Article found and returning:', article.title || article.slug);
            const response: NewsAPIResponse = {
              success: true,
              article: article
            };

            return NextResponse.json(response);
          } else {
            console.log('⚠️ No article data in response:', backendData);
          }
        } else {
          console.log(`⚠️ Backend returned ${backendResponse.status}: ${backendResponse.statusText}`);
        }
      } catch (endpointError) {
        console.log(`⚠️ Endpoint ${endpoint} failed:`, endpointError);
        continue;
      }
    }

    console.log(`❌ All backend endpoints failed for article: ${slug}`);

    // Return 404 when article not found in real API
    const response: NewsAPIResponse = {
      success: false,
      article: null,
      message: 'Article not found'
    };

    return NextResponse.json(response, { status: 404 });

  } catch (error) {
    console.error('❌ News article API error:', error);

    const response: NewsAPIResponse = {
      success: false,
      article: null,
      message: 'Internal server error'
    };

    return NextResponse.json(response, { status: 500 });
  }
}
