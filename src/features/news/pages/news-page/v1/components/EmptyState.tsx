'use client';

import React from 'react';

interface EmptyStateProps {
  selectedCategory: string;
  searchQuery: string;
}

/**
 * Empty State Component
 * 
 * Displays when no news articles are found for the current filters.
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  selectedCategory,
  searchQuery
}) => {
  const isSearching = Boolean(searchQuery);
  const isFiltered = selectedCategory !== 'all';

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div className="max-w-md mx-auto">
        {/* Empty Icon */}
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
          <svg
            className="h-8 w-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
            />
          </svg>
        </div>

        {/* Empty Message */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {isSearching ? 'No Search Results' : 'No Articles Found'}
        </h3>
        
        <p className="text-gray-600 mb-6">
          {isSearching ? (
            <>
              No articles found for "<strong>{searchQuery}</strong>".
              {isFiltered && (
                <> Try searching in a different category or remove filters.</>
              )}
            </>
          ) : isFiltered ? (
            <>
              No articles found in the <strong>{selectedCategory}</strong> category.
              Try selecting a different category or check back later.
            </>
          ) : (
            'No news articles are available at the moment. Please check back later.'
          )}
        </p>

        {/* Suggestions */}
        <div className="space-y-3">
          {isSearching && (
            <div className="text-sm text-gray-500">
              <p className="mb-2">Try:</p>
              <ul className="space-y-1">
                <li>• Using different keywords</li>
                <li>• Checking your spelling</li>
                <li>• Using more general terms</li>
                {isFiltered && <li>• Searching in all categories</li>}
              </ul>
            </div>
          )}
          
          {!isSearching && isFiltered && (
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
