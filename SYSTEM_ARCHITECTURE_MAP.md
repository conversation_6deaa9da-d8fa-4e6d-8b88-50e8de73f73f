# 🏗️ System Architecture Map & Workflow Documentation

## 📋 Project Overview

**Sports News Platform** - Next.js 15 TypeScript application with Hybrid Feature-Module Architecture
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Architecture**: Hybrid Feature-Module with Version Management
- **Styling**: Tailwind CSS
- **State Management**: React Hooks + Context
- **API Strategy**: Next.js API Routes as Proxy Layer

---

## 🗂️ Complete Directory Structure

```
/
├── .augment-rules.md              # Architecture guidelines & rules
├── REFACTOR_SUMMARY.md            # Refactoring documentation
├── SYSTEM_ARCHITECTURE_MAP.md     # This file
├── package.json                   # Dependencies & scripts
├── tailwind.config.ts             # Tailwind configuration
├── next.config.js                 # Next.js configuration
├── tsconfig.json                  # TypeScript configuration
└── src/
    ├── app/                       # Next.js App Router (Routing Layer)
    │   ├── layout.tsx             # Root layout
    │   ├── page.tsx               # Home route → features/home/<USER>
    │   ├── globals.css            # Global styles
    │   ├── news/
    │   │   ├── page.tsx           # News listing → features/news/pages
    │   │   └── [slug]/
    │   │       └── page.tsx       # News detail → features/news/pages
    │   └── api/                   # API Proxy Layer
    │       ├── football/
    │       │   ├── fixtures/route.ts    # Fixtures proxy
    │       │   └── leagues/route.ts     # Leagues proxy
    │       └── news/
    │           ├── route.ts             # News list proxy
    │           ├── categories/route.ts  # Categories proxy
    │           ├── article/[slug]/route.ts  # Article detail proxy
    │           └── related/[slug]/route.ts  # Related articles proxy
    ├── features/                  # Feature Modules (Business Domains)
    │   ├── home/                  # Home Feature
    │   │   ├── config.ts          # Version configuration
    │   │   ├── index.ts           # Feature exports
    │   │   ├── types.ts           # Feature types
    │   │   ├── pages/             # Page components
    │   │   │   ├── index.ts       # Page exports
    │   │   │   └── home-page/
    │   │   │       ├── index.ts   # Version management
    │   │   │       ├── v1/        # V1 implementation
    │   │   │       └── README.md  # Documentation
    │   │   ├── components/        # Feature components
    │   │   │   ├── hero/          # Hero section (v1-v5)
    │   │   │   ├── content-sections/  # Breaking News, Upcoming Fixtures
    │   │   │   ├── engagement-features/  # Match Highlights, Stats (v1-v2)
    │   │   │   └── footer-content/    # Footer (v1-v2)
    │   │   └── hooks/             # Feature hooks
    │   │       └── useMagneticFieldSystem.ts  # Advanced 3D effects
    │   ├── news/                  # News Feature
    │   │   ├── config.ts          # Version configuration
    │   │   ├── index.ts           # Feature exports
    │   │   ├── types.ts           # Feature types
    │   │   └── pages/             # Page components
    │   │       ├── index.ts       # Page exports
    │   │       ├── news-page/     # News listing page
    │   │       │   ├── index.ts   # Version management
    │   │       │   └── v1/        # V1 implementation
    │   │       │       ├── NewsPageV1.tsx
    │   │       │       ├── components/  # Page-specific components
    │   │       │       ├── hooks/       # Page-specific hooks
    │   │       │       └── types/       # Page-specific types
    │   │       └── news-detail-page/  # News detail page
    │   │           ├── index.ts   # Version management
    │   │           └── v1/        # V1 implementation
    │   │               └── NewsDetailPageV1.tsx
    │   ├── fixture/               # Fixture Feature (Planned)
    │   │   ├── config.ts          # Version configuration
    │   │   ├── index.ts           # Feature exports
    │   │   └── types.ts           # Feature types
    │   └── league/                # League Feature (Planned)
    │       ├── config.ts          # Version configuration
    │       ├── index.ts           # Feature exports
    │       └── types.ts           # Feature types
    ├── shared/                    # Global Shared Resources
    │   ├── components/            # Reusable UI components
    │   │   ├── ui/                # Basic UI primitives
    │   │   ├── layout/            # Layout components
    │   │   └── forms/             # Form components
    │   ├── hooks/                 # Global hooks
    │   ├── types/                 # Global types
    │   └── utils/                 # Utility functions
    ├── lib/                       # External Integrations
    │   ├── api/                   # API clients
    │   │   ├── fixtures.ts        # Fixtures API client
    │   │   └── news.ts            # News API client
    │   └── utils/                 # Library utilities
    └── types/                     # Global type definitions
        └── global.d.ts            # Global TypeScript definitions
```

---

## 🎯 Feature Module Architecture

### 🏠 Home Feature (Complete)
**Location**: `src/features/home/<USER>
**Status**: ✅ Production Ready

#### Configuration
```typescript
// src/features/home/<USER>
export const homeFeatureConfig = {
  pages: {
    homePage: 'v1'
  },
  components: {
    hero: 'v5',               // Latest version
    breakingNews: 'v1',
    upcomingFixtures: 'v2',   // Enhanced version
    engagementFeatures: 'v2', // Enhanced version
    footerContent: 'v2'       // Minimalist design
  }
}
```

#### Components Structure
```
home/components/
├── hero/                    # Hero Section (5 versions)
│   ├── index.ts            # Version management
│   ├── v1/ → v5/           # Progressive versions
│   └── README.md           # Documentation
├── content-sections/        # Content components
│   ├── breaking-news/v1/   # Breaking news
│   └── upcoming-fixtures/  # Fixtures (v1-v2)
├── engagement-features/     # Interactive features
│   ├── v1/                 # Match Highlights, Stats, Social Feed
│   └── v2/                 # Enhanced UI/UX with glassmorphism
└── footer-content/         # Footer components
    ├── v1/                 # Initial implementation
    └── v2/                 # Minimalist design
```

#### Advanced Features
- **Magnetic Field System**: 3D interactive effects with performance optimization
- **Real-time Data**: Live fixtures and breaking news
- **Version Management**: 5 hero versions with different designs

### 📰 News Feature (Complete)
**Location**: `src/features/news/`
**Status**: ✅ Production Ready

#### Configuration
```typescript
// src/features/news/config.ts
export const newsFeatureConfig = {
  pages: {
    newsPage: 'v1',    // 2-column layout with categories
    newsDetail: 'v1'   // Article detail with sidebar
  },
  components: {
    newsCard: 'v1',
    categoryFilter: 'v1',
    searchBar: 'v1',
    newsGrid: 'v1',
    pagination: 'v1'
  }
}
```

#### Pages Structure
```
news/pages/
├── news-page/              # News listing page
│   ├── index.ts           # Version management
│   └── v1/                # 2-column layout
│       ├── NewsPageV1.tsx
│       ├── components/    # NewsCard, CategorySidebar, SearchBar
│       ├── hooks/         # useNews, useInfiniteScroll
│       └── types/         # Page-specific types
└── news-detail-page/      # News article detail
    ├── index.ts           # Version management
    └── v1/                # Article detail with sidebar
        └── NewsDetailPageV1.tsx
```

#### Key Features
- **2-Column Layout**: Categories sidebar + content area
- **Real API Integration**: Multiple endpoint fallback strategy
- **SEO Optimization**: Dynamic metadata, structured data
- **Infinite Scroll**: Progressive loading with performance optimization

### ⚽ Fixture Feature (Planned)
**Location**: `src/features/fixture/`
**Status**: 🚧 Configuration Ready

### 🏆 League Feature (Planned)
**Location**: `src/features/league/`
**Status**: 🚧 Configuration Ready

---

## 🔌 API Architecture

### Proxy Layer Strategy
All API calls go through Next.js API routes for security and caching:

```
Frontend → Next.js API Routes → Backend API → Database
```

### API Routes Map

#### Football APIs
```typescript
// Fixtures API
GET /api/football/fixtures
- Params: page, limit, league, date, leagueId
- Proxy to: http://localhost:3000/football/fixtures
- Features: Backend filtering, caching, CORS

// Leagues API  
GET /api/football/leagues
- Params: active
- Proxy to: http://localhost:3000/football/leagues
- Features: Caching, error handling
```

#### News APIs
```typescript
// News List API
GET /api/news
- Params: page, limit, category, search
- Multiple endpoints fallback:
  - /news/articles
  - /news/published  
  - /news
  - /public/news
- Fallback: Mock data for development

// News Categories API
GET /api/news/categories
- Direct proxy to: /news/categories
- Real backend integration

// Article Detail API
GET /api/news/article/[slug]
- Multiple endpoints fallback:
  - /news/article/{slug}
  - /news/articles/{slug}
  - /public/news/article/{slug}
  - /public/news/articles/{slug}
- Fallback: Mock data

// Related Articles API
GET /api/news/related/[slug]
- Multiple endpoints fallback:
  - /news/related/{slug}
  - /news/articles/related/{slug}
  - /public/news/related/{slug}
  - /news/similar/{slug}
- Fallback: Mock data
```

### API Client Libraries
```typescript
// src/lib/api/fixtures.ts
- fetchFixtures()
- fetchFixturesByDate()
- fetchFixturesByLeague()

// src/lib/api/news.ts  
- fetchNews()
- fetchFeaturedNews()
- fetchBreakingNews()
- fetchNewsCategories()
- fetchArticleBySlug()
- fetchRelatedArticles()
```

---

## 🔄 Data Flow Patterns

### 1. Page Load Flow
```
1. Next.js Route (app/page.tsx)
2. Feature Page Component (features/home/<USER>/home-page/v1/)
3. API Client (lib/api/)
4. Next.js API Route (app/api/)
5. Backend API (localhost:3000)
6. Component State Update
7. UI Render
```

### 2. Version Management Flow
```
1. Config File (features/*/config.ts)
2. Version Import (features/*/pages/index.ts)
3. Dynamic Component Loading
4. Render Active Version
```

### 3. Error Handling Flow
```
1. API Request Fails
2. Try Multiple Endpoints
3. Fallback to Mock Data
4. Display Error State
5. Log Error Details
```

---

## 🎨 Component Organization Levels

### Global Shared Components
**Location**: `src/shared/components/`
- **Purpose**: Reusable across all features
- **Examples**: Button, Input, Modal, Container
- **Characteristics**: Generic, configurable, no business logic

### Feature-Level Components  
**Location**: `src/features/<feature>/components/`
- **Purpose**: Reusable within specific feature
- **Examples**: NewsCard, FixtureCard, LeagueTable
- **Characteristics**: Domain-specific, can contain business logic
- **Versioning**: v1, v2, v3... with index.ts management

### Page-Level Components
**Location**: `src/features/<feature>/pages/<page>/v1/components/`
- **Purpose**: Specific to individual page
- **Examples**: CategorySidebar, SearchBar, ArticleHeader
- **Characteristics**: Page-specific, tightly coupled
- **Versioning**: No versioning (tied to page version)

---

## ⚙️ Version Management System

### Configuration-Based Versioning
```typescript
// Central configuration
export const homeFeatureConfig = {
  components: {
    hero: 'v5',               // Switch versions easily
    upcomingFixtures: 'v2'
  }
} as const;

// Dynamic imports
export const getHeroComponent = () => {
  const version = homeFeatureConfig.components.hero;
  return import(`./hero/v${version}`).then(m => m.default);
};
```

### Version Metadata
```typescript
export const HERO_VERSION_INFO = {
  v1: { name: 'Classic Hero', status: 'stable' },
  v2: { name: 'Enhanced Hero', status: 'stable' },
  v3: { name: 'Interactive Hero', status: 'stable' },
  v4: { name: 'Advanced Hero', status: 'stable' },
  v5: { name: 'Premium Hero', status: 'stable' }
} as const;
```

### Environment-Based Configuration
```typescript
export const FEATURE_VERSIONS = {
  development: { hero: 'v5' },
  production: { hero: 'v4' }
} as const;
```

---

## 🚀 Performance Optimizations

### Caching Strategy
```typescript
// API Routes Caching
headers: {
  'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300'
}

// Next.js Revalidation
next: { revalidate: 300 } // 5 minutes
```

### Code Splitting
- **Dynamic Imports**: Version-based component loading
- **Route-Based**: Automatic Next.js code splitting
- **Feature-Based**: Each feature is independently bundled

### Image Optimization
- **CDN Integration**: `NEXT_PUBLIC_CDN_DOMAIN_PICTURE`
- **Next.js Image**: Automatic optimization
- **Fallback Images**: Error handling with placeholder images

### Mobile Optimization
- **Responsive Design**: Mobile-first approach
- **Performance Mode**: Reduced animations on mobile
- **Battery Optimization**: Automatic detection and optimization

---

## 🔧 Development Workflow

### Adding New Features
1. Create feature directory: `src/features/<feature-name>/`
2. Add configuration: `config.ts`
3. Create page structure: `pages/<page-name>/v1/`
4. Implement components: `components/`
5. Add API integration: `lib/api/`
6. Update routing: `app/<route>/page.tsx`

### Version Management
1. Update config: Change version number
2. Create new version: `v2/` directory
3. Implement changes: New component version
4. Test thoroughly: Ensure backward compatibility
5. Deploy gradually: Environment-based rollout

### API Integration
1. Create API route: `app/api/<endpoint>/route.ts`
2. Implement proxy logic: Multiple endpoint fallback
3. Add client library: `lib/api/<service>.ts`
4. Handle errors: Graceful fallback to mock data
5. Add caching: Performance optimization

---

## 📊 Current System Status

### ✅ Completed Features
- **Home Feature**: Complete with 5 hero versions
- **News Feature**: Complete with 2-column layout
- **API Proxy Layer**: Complete with fallback strategy
- **Version Management**: Complete system implementation
- **Performance Optimization**: Caching, code splitting, mobile optimization

### 🚧 In Development
- **Fixture Feature**: Configuration ready
- **League Feature**: Configuration ready
- **Advanced Analytics**: Performance monitoring
- **SEO Enhancement**: Advanced metadata optimization

### 📋 Planned Features
- **User Authentication**: Login/register system
- **Personalization**: User preferences and favorites
- **Real-time Updates**: WebSocket integration
- **Progressive Web App**: PWA capabilities
- **Dark Mode**: Theme switching system

---

## 🎯 Key Benefits

### Architecture Benefits
- **Scalability**: Easy to add new features and versions
- **Maintainability**: Clear separation of concerns
- **Flexibility**: Version switching without code changes
- **Performance**: Optimized loading and caching
- **Developer Experience**: Clear structure and documentation

### Business Benefits
- **Fast Development**: Reusable components and patterns
- **Easy A/B Testing**: Version-based feature flags
- **Gradual Rollouts**: Environment-based deployments
- **SEO Optimization**: Built-in SEO best practices
- **Mobile-First**: Optimized for all devices

---

## 📚 Documentation References

- **Architecture Rules**: `.augment-rules.md`
- **Refactor Summary**: `REFACTOR_SUMMARY.md`
- **Component Documentation**: Individual `README.md` files
- **API Documentation**: Inline comments in API routes
- **Type Definitions**: TypeScript interfaces and types

---

## 🔄 System Workflow Diagrams

### 1. Request Flow Diagram
```mermaid
graph TD
    A[User Request] --> B[Next.js App Router]
    B --> C[Feature Page Component]
    C --> D[API Client Library]
    D --> E[Next.js API Route]
    E --> F{Backend Available?}
    F -->|Yes| G[Backend API]
    F -->|No| H[Mock Data Fallback]
    G --> I[Response Processing]
    H --> I
    I --> J[Component State Update]
    J --> K[UI Render]
```

### 2. Version Management Flow
```mermaid
graph LR
    A[Config File] --> B[Version Import]
    B --> C[Dynamic Loading]
    C --> D[Component Render]

    E[Environment] --> F[Config Selection]
    F --> A

    G[Feature Flag] --> H[Version Override]
    H --> B
```

### 3. Error Handling Flow
```mermaid
graph TD
    A[API Request] --> B{Endpoint 1 OK?}
    B -->|Yes| C[Success Response]
    B -->|No| D{Endpoint 2 OK?}
    D -->|Yes| C
    D -->|No| E{Endpoint 3 OK?}
    E -->|Yes| C
    E -->|No| F{Endpoint 4 OK?}
    F -->|Yes| C
    F -->|No| G[Mock Data Fallback]
    G --> H[Log Error]
    H --> I[Display Fallback UI]
```

---

## 📋 API Endpoints Documentation

### Football APIs

#### GET /api/football/fixtures
**Purpose**: Fetch football fixtures with filtering
**Backend Endpoints**:
- `http://localhost:3000/football/fixtures` (with filters)
- `http://localhost:3000/football/fixtures/upcoming-and-live` (default)

**Parameters**:
```typescript
interface FixturesParams {
  page?: number;        // Default: 1
  limit?: number;       // Default: 100
  league?: string;      // League name (client-side filter)
  leagueId?: number;    // League ID (backend filter)
  date?: string;        // Date filter (YYYY-MM-DD)
}
```

**Response Format**:
```typescript
interface FixturesResponse {
  data: Fixture[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}
```

**Caching**: 30 seconds with stale-while-revalidate
**Error Handling**: 504 for timeout, 500 for server errors

#### GET /api/football/leagues
**Purpose**: Fetch football leagues
**Backend Endpoint**: `http://localhost:3000/football/leagues`

**Parameters**:
```typescript
interface LeaguesParams {
  active?: boolean;     // Default: true
}
```

**Caching**: 5 minutes with stale-while-revalidate

### News APIs

#### GET /api/news
**Purpose**: Fetch news articles with pagination and filtering
**Backend Endpoints** (fallback order):
1. `http://localhost:3000/news/articles`
2. `http://localhost:3000/news/published`
3. `http://localhost:3000/news`
4. `http://localhost:3000/public/news`

**Parameters**:
```typescript
interface NewsParams {
  page?: number;        // Default: 1
  limit?: number;       // Default: 10
  category?: string;    // Category slug
  search?: string;      // Search query
}
```

**Response Format**:
```typescript
interface NewsResponse {
  data: Article[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}
```

**Fallback**: Mock data with 5 sample articles
**Caching**: 60 seconds for real data, 30 seconds for mock data

#### GET /api/news/categories
**Purpose**: Fetch news categories
**Backend Endpoint**: `http://localhost:3000/news/categories`

**Response Format**:
```typescript
interface Category {
  id: number;
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  sortOrder: number;
  isActive: boolean;
  isPublic: boolean;
  articleCount: number;
  publishedArticleCount: number;
  createdAt: string;
  updatedAt: string;
}
```

**Status**: ✅ Working with real backend data

#### GET /api/news/article/[slug]
**Purpose**: Fetch individual news article by slug
**Backend Endpoints** (fallback order):
1. `http://localhost:3000/news/article/{slug}`
2. `http://localhost:3000/news/articles/{slug}`
3. `http://localhost:3000/public/news/article/{slug}`
4. `http://localhost:3000/public/news/articles/{slug}`

**Response Format**:
```typescript
interface ArticleResponse {
  success: boolean;
  article: Article | null;
  message?: string;
}
```

**Fallback**: Mock data for development
**Caching**: 5 minutes with Next.js revalidation

#### GET /api/news/related/[slug]
**Purpose**: Fetch related articles for a specific article
**Backend Endpoints** (fallback order):
1. `http://localhost:3000/news/related/{slug}?limit=4`
2. `http://localhost:3000/news/articles/related/{slug}?limit=4`
3. `http://localhost:3000/public/news/related/{slug}?limit=4`
4. `http://localhost:3000/news/similar/{slug}?limit=4`

**Response Format**:
```typescript
interface RelatedArticlesResponse {
  success: boolean;
  articles: RelatedArticle[];
  message?: string;
}
```

**Fallback**: Mock data for development
**Caching**: 10 minutes with Next.js revalidation

---

## 🔧 Development Commands

### Project Setup
```bash
npm install                 # Install dependencies
npm run dev                # Start development server (port 5000)
npm run build              # Build for production
npm run start              # Start production server
npm run lint               # Run ESLint
npm run type-check         # TypeScript type checking
```

### Development Workflow
```bash
# Start development
npm run dev

# Backend API (separate terminal)
# Ensure backend is running on localhost:3000

# Environment variables
NEXT_PUBLIC_CDN_DOMAIN_PICTURE=http://**************/
BASE_URL=http://localhost:3000
BACKEND_API_URL=http://localhost:3000
PORT=5000
```

### Testing URLs
```bash
# Home page
http://localhost:5000/

# News listing
http://localhost:5000/news

# News detail (working articles)
http://localhost:5000/news/manchester-united-signs-mbappe-record-deal
http://localhost:5000/news/liverpool-defeats-arsenal-premier-league
http://localhost:5000/news/champions-league-draw-exciting-matchups

# API endpoints
http://localhost:5000/api/football/fixtures
http://localhost:5000/api/football/leagues
http://localhost:5000/api/news
http://localhost:5000/api/news/categories
```

---

## 🐛 Debugging Guide

### Common Issues & Solutions

#### 1. API Connection Issues
**Symptoms**: API calls failing, using mock data
**Check**:
- Backend server running on localhost:3000
- Environment variables set correctly
- Network connectivity
- CORS headers in API routes

**Debug Commands**:
```bash
curl http://localhost:3000/news/categories
curl http://localhost:3000/football/fixtures
```

#### 2. Version Management Issues
**Symptoms**: Component not loading, import errors
**Check**:
- Config file version numbers
- Component directory structure
- Index.ts exports
- Dynamic import paths

**Debug Steps**:
1. Check `features/*/config.ts`
2. Verify component directory exists
3. Check index.ts exports
4. Test dynamic imports

#### 3. Image Loading Issues
**Symptoms**: Images not displaying, 404 errors
**Check**:
- CDN environment variable
- Image paths in API responses
- Fallback image handling
- Network connectivity to CDN

**Debug**:
```typescript
console.log('CDN Domain:', process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE);
console.log('Image URL:', imageUrl);
```

#### 4. Performance Issues
**Symptoms**: Slow loading, high memory usage
**Check**:
- Caching headers
- Bundle size
- Image optimization
- API response times

**Debug Tools**:
- Next.js Bundle Analyzer
- Chrome DevTools Performance
- Network tab for API timing
- Memory tab for leaks

---

## 📈 Performance Monitoring

### Key Metrics to Track
- **Page Load Time**: < 3 seconds
- **API Response Time**: < 500ms
- **Bundle Size**: < 1MB initial load
- **Memory Usage**: < 100MB
- **Cache Hit Rate**: > 80%

### Monitoring Tools
- **Next.js Analytics**: Built-in performance monitoring
- **Chrome DevTools**: Performance profiling
- **Lighthouse**: SEO and performance audits
- **Bundle Analyzer**: Code splitting analysis

---

## 🔒 Security Considerations

### API Security
- **Proxy Layer**: All external APIs go through Next.js routes
- **Environment Variables**: Sensitive data in .env files
- **CORS Headers**: Proper cross-origin configuration
- **Rate Limiting**: Timeout controls on API calls

### Data Validation
- **TypeScript**: Compile-time type checking
- **API Response Validation**: Runtime type checking
- **Input Sanitization**: XSS prevention
- **Error Handling**: No sensitive data in error messages

---

## 🚀 Deployment Guide

### Environment Setup
```bash
# Production environment variables
NEXT_PUBLIC_CDN_DOMAIN_PICTURE=https://cdn.yourdomain.com/
BASE_URL=https://api.yourdomain.com
BACKEND_API_URL=https://api.yourdomain.com
NODE_ENV=production
```

### Build Process
```bash
npm run build              # Build production bundle
npm run start              # Start production server
```

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Backend API accessible
- [ ] CDN images accessible
- [ ] SSL certificates installed
- [ ] Performance monitoring enabled
- [ ] Error tracking configured

---

*This document serves as the complete system map for development, debugging, and feature enhancement.*
