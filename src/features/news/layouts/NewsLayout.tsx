'use client';

import React, { useState, useEffect } from 'react';
import { CategorySidebar } from '../pages/news-page/v1/components/CategorySidebar';
import { SearchBar } from '../pages/news-page/v1/components/SearchBar';
import { fetchNewsCategories, type NewsCategory } from '@/lib/api/newsCategories';

interface NewsLayoutProps {
  children: React.ReactNode;
  selectedCategory?: string;
  onCategoryChange?: (category: string) => void;
  showSearch?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  onSearchSubmit?: (query: string) => void;
  className?: string;
}

/**
 * News Layout - Shared Layout for News Pages
 * 
 * Features:
 * - Consistent 2-column layout (categories left, content right)
 * - Shared category sidebar
 * - Optional search functionality
 * - Responsive design
 * - High performance with memoization
 * 
 * @example
 * ```tsx
 * <NewsLayout 
 *   selectedCategory="football"
 *   onCategoryChange={handleCategoryChange}
 *   showSearch={true}
 * >
 *   <YourContentComponent />
 * </NewsLayout>
 * ```
 */
function NewsLayout({
  children,
  selectedCategory = 'all',
  onCategoryChange,
  showSearch = true,
  searchQuery = '',
  onSearchChange,
  onSearchSubmit,
  className = ''
}: NewsLayoutProps) {
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // Fetch categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        console.log('🔄 Fetching categories for layout');
        const response = await fetchNewsCategories();
        setCategories(response.data);
        console.log('✅ Categories loaded for layout:', response.data.length);
      } catch (error) {
        console.error('❌ Error loading categories for layout:', error);
        setCategories([]);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Handle category change
  const handleCategoryChange = (category: string) => {
    console.log('📂 Category changed in layout:', category);
    onCategoryChange?.(category);
  };

  // Handle search
  const handleSearchChange = (query: string) => {
    console.log('🔍 Search changed in layout:', query);
    onSearchChange?.(query);
  };

  const handleSearchSubmit = (query: string) => {
    console.log('🔍 Search submitted in layout:', query);
    onSearchSubmit?.(query);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900">Sports News</h1>
          <p className="mt-2 text-gray-600">Stay updated with the latest sports news and updates</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 min-w-0">
          {/* Left Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              {/* Categories */}
              <CategorySidebar
                categories={categories}
                selectedCategory={selectedCategory}
                onCategoryChange={handleCategoryChange}
                isLoading={isLoadingCategories}
              />

              {/* Desktop Search */}
              {showSearch && (
                <div className="hidden md:block mt-6 pt-6 border-t border-gray-200">
                  <SearchBar
                    value={searchQuery}
                    onChange={handleSearchChange}
                    onSubmit={handleSearchSubmit}
                    placeholder="Search news..."
                  />
                </div>
              )}

              {/* Mobile Search */}
              {showSearch && (
                <div className="md:hidden mt-4 pt-4 border-t border-gray-200">
                  <SearchBar
                    value={searchQuery}
                    onChange={handleSearchChange}
                    onSubmit={handleSearchSubmit}
                    placeholder="Search news..."
                  />
                </div>
              )}
            </div>
          </div>

          {/* Right Content - Dynamic Content */}
          <div className="lg:col-span-3 min-w-0">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Memoized version for performance
export const MemoizedNewsLayout = React.memo(NewsLayout);

// Export default
export default NewsLayout;
