// News Feature Types
// This file contains all TypeScript type definitions for the news feature

// Re-export types from API
export type { NewsArticle, NewsAPIResponse, NewsAPIParams } from '@/lib/api/news';

// Import for internal use
import type { NewsArticle } from '@/lib/api/news';

// Page-specific types
export interface NewsPageProps {
        className?: string;
        initialData?: NewsArticle[];
}

export interface NewsDetailProps {
        slug: string;
        className?: string;
}

// Component-specific types
export interface NewsCardProps {
        article: NewsArticle;
        variant?: 'default' | 'featured' | 'compact';
        className?: string;
}

export interface CategoryFilterProps {
        categories: string[];
        selectedCategory?: string;
        onCategoryChange: (category: string) => void;
        className?: string;
}

export interface SearchBarProps {
        value: string;
        onChange: (value: string) => void;
        onSubmit: (value: string) => void;
        placeholder?: string;
        className?: string;
}
