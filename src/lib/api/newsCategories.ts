// News Categories API Client - Utility functions for news categories API calls via proxy
// This module provides a clean interface for news categories operations

export interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  count: number;
  sortOrder: number;
}

export interface NewsCategoriesAPIResponse {
  data: NewsCategory[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

export interface NewsCategoriesAPIError {
  error: string;
  message: string;
  code: string;
}

/**
 * Fetch news categories via Next.js API proxy
 * @returns Promise<NewsCategoriesAPIResponse>
 */
export async function fetchNewsCategories(): Promise<NewsCategoriesAPIResponse> {
  try {
    const url = '/api/news/categories';

    console.log('📂 Fetching news categories via proxy:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for better performance
      cache: 'default',
    });

    if (!response.ok) {
      const errorData: NewsCategoriesAPIError = await response.json().catch(() => ({
        error: 'Unknown error',
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: 'HTTP_ERROR'
      }));

      throw new Error(errorData.message || `Failed to fetch news categories: ${response.status}`);
    }

    const data: NewsCategoriesAPIResponse = await response.json();

    console.log('✅ News categories fetched successfully:', data.data.length, 'categories');

    return data;

  } catch (error) {
    console.error('❌ Error fetching news categories:', error);

    // Re-throw with enhanced error information
    if (error instanceof Error) {
      throw new Error(`News Categories API Error: ${error.message}`);
    }

    throw new Error('Unknown error occurred while fetching news categories');
  }
}

/**
 * Get active news categories (client-side filtering)
 * @returns Promise<NewsCategoriesAPIResponse>
 */
export async function fetchActiveNewsCategories(): Promise<NewsCategoriesAPIResponse> {
  const response = await fetchNewsCategories();
  
  // Filter for categories with articles
  const activeCategories = response.data.filter(category => category.count > 0);

  return {
    ...response,
    data: activeCategories,
    meta: {
      ...response.meta,
      totalItems: activeCategories.length,
      limit: activeCategories.length
    }
  };
}

/**
 * Get category by slug
 * @param slug - Category slug to find
 * @returns Promise<NewsCategory | null>
 */
export async function fetchCategoryBySlug(slug: string): Promise<NewsCategory | null> {
  try {
    const response = await fetchNewsCategories();
    const category = response.data.find(cat => cat.slug === slug);
    return category || null;
  } catch (error) {
    console.error('❌ Error fetching category by slug:', error);
    return null;
  }
}

/**
 * Check news categories API health via proxy
 * @returns Promise<boolean>
 */
export async function checkNewsCategoriesAPIHealth(): Promise<boolean> {
  try {
    const response = await fetch('/api/news/categories', {
      method: 'HEAD',
      cache: 'no-cache'
    });

    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get news categories API status information
 * @returns Promise<{healthy: boolean, message: string}>
 */
export async function getNewsCategoriesAPIStatus(): Promise<{ healthy: boolean, message: string }> {
  try {
    const healthy = await checkNewsCategoriesAPIHealth();

    if (healthy) {
      return {
        healthy: true,
        message: 'News Categories API is operational'
      };
    } else {
      return {
        healthy: false,
        message: 'News Categories API is currently unavailable'
      };
    }
  } catch (error) {
    return {
      healthy: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Export types for use in components
export type { NewsCategory, NewsCategoriesAPIResponse, NewsCategoriesAPIError };
