// News Error Utilities - Shared error handling utilities
// This module provides consistent error handling across news components

export interface NewsError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: any;
}

/**
 * Parse and normalize error from different sources
 * @param error - Error from API call or other source
 * @returns Normalized NewsError object
 */
export const parseNewsError = (error: unknown): NewsError => {
  // Handle Error instances
  if (error instanceof Error) {
    return {
      message: error.message,
      code: 'CLIENT_ERROR',
      details: error
    };
  }

  // Handle API error responses
  if (typeof error === 'object' && error !== null) {
    const apiError = error as any;
    
    if (apiError.message) {
      return {
        message: apiError.message,
        code: apiError.code || 'API_ERROR',
        statusCode: apiError.statusCode || apiError.status,
        details: apiError
      };
    }
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      message: error,
      code: 'UNKNOWN_ERROR'
    };
  }

  // Fallback for unknown error types
  return {
    message: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    details: error
  };
};

/**
 * Get user-friendly error message based on error type
 * @param error - NewsError object
 * @returns User-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: NewsError): string => {
  switch (error.code) {
    case 'TIMEOUT_ERROR':
      return 'The request is taking too long. Please try again.';
    
    case 'SERVICE_UNAVAILABLE':
      return 'The news service is temporarily unavailable. Please try again later.';
    
    case 'NETWORK_ERROR':
      return 'Network connection error. Please check your internet connection.';
    
    case 'NOT_FOUND':
      return 'The requested content was not found.';
    
    case 'UNAUTHORIZED':
      return 'You are not authorized to access this content.';
    
    case 'FORBIDDEN':
      return 'Access to this content is forbidden.';
    
    case 'RATE_LIMITED':
      return 'Too many requests. Please wait a moment and try again.';
    
    case 'VALIDATION_ERROR':
      return 'Invalid request. Please check your input and try again.';
    
    default:
      // Return the original message if it's user-friendly, otherwise use generic message
      if (error.message && !error.message.includes('fetch') && !error.message.includes('API')) {
        return error.message;
      }
      return 'Something went wrong. Please try again.';
  }
};

/**
 * Check if error is retryable
 * @param error - NewsError object
 * @returns True if error is retryable
 */
export const isRetryableError = (error: NewsError): boolean => {
  const retryableCodes = [
    'TIMEOUT_ERROR',
    'SERVICE_UNAVAILABLE',
    'NETWORK_ERROR',
    'RATE_LIMITED'
  ];
  
  return retryableCodes.includes(error.code || '');
};

/**
 * Log error with context for debugging
 * @param error - Error to log
 * @param context - Additional context information
 */
export const logNewsError = (error: NewsError, context?: Record<string, any>): void => {
  const logData = {
    timestamp: new Date().toISOString(),
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    },
    context
  };

  // In development, log to console with full details
  if (process.env.NODE_ENV === 'development') {
    console.error('🚨 News Error:', logData);
    if (error.details) {
      console.error('📋 Error Details:', error.details);
    }
  } else {
    // In production, you might want to send to error tracking service
    console.error('News Error:', error.message);
  }
};

/**
 * Create error handler function for React components
 * @param onError - Optional error callback
 * @returns Error handler function
 */
export const createErrorHandler = (
  onError?: (error: NewsError) => void
) => {
  return (error: unknown, context?: Record<string, any>) => {
    const newsError = parseNewsError(error);
    logNewsError(newsError, context);
    
    if (onError) {
      onError(newsError);
    }
    
    return newsError;
  };
};

/**
 * Retry function with exponential backoff
 * @param fn - Function to retry
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @returns Promise that resolves with function result
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Check if error is retryable
      const newsError = parseNewsError(error);
      if (!isRetryableError(newsError)) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};
