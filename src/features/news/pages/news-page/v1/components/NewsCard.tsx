'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { SocialShare } from './SocialShare';
import { useReadingAnalytics } from '../hooks/useReadingAnalytics';
import type { NewsArticle } from '@/lib/api/news';
import { getImageUrl, handleImageError } from '@/features/news/utils/imageUtils';
import { getCategoryIcon, getCategoryColors } from '@/features/news/utils/categoryUtils';

interface NewsCardProps {
  article: NewsArticle;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
}

/**
 * News Card Component
 * 
 * Displays a news article with image, title, excerpt, and metadata.
 * Supports different variants for different layouts.
 */
export const NewsCard: React.FC<NewsCardProps> = ({
  article,
  variant = 'default',
  className = ''
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  // Analytics tracking
  useReadingAnalytics({
    articleId: article.id.toString(),
    title: article.title,
    category: article.category?.name || 'general'
  });

  // Use shared utilities for consistent handling
  const imageUrl = getImageUrl(article.featuredImage);
  const categoryIcon = getCategoryIcon(article.category?.slug || 'general');
  const categoryColors = getCategoryColors(article.category?.slug || 'general');

  const timeAgo = formatDistanceToNow(new Date(article.publishedAt), { addSuffix: true });
  const readTime = Math.ceil(article.excerpt.length / 200); // Rough estimate
  const articleUrl = `${typeof window !== 'undefined' ? window.location.origin : ''}/news/${article.slug}`;

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsBookmarked(!isBookmarked);

    // Track bookmark action
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', isBookmarked ? 'remove_bookmark' : 'add_bookmark', {
        article_id: article.id.toString(),
        article_title: article.title
      });
    }
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowShareMenu(!showShareMenu);
  };

  if (variant === 'featured') {
    return (
      <Link href={`/news/${article.slug}`} className={`block ${className}`}>
        <article className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
          {/* Featured Image */}
          <div className="relative h-64 md:h-80 overflow-hidden">
            <img
              src={imageUrl}
              alt={article.title}
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
              onError={(e) => handleImageError(e, 'News+Image')}
            />
            {article.isFeatured && (
              <div className="absolute top-4 left-4">
                <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Featured
                </span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="flex items-center space-x-2 mb-3">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors}`}>
                <span className="mr-1">
                  {categoryIcon}
                </span>
                {article.category?.name || 'General'}
              </span>
              {article.priority >= 7 && (
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">
                  Breaking
                </span>
              )}
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-3 line-clamp-2">
              {article.title}
            </h2>

            <p className="text-gray-600 mb-4 line-clamp-3">
              {article.excerpt}
            </p>

            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center space-x-4">
                <span>📅 {timeAgo}</span>
                <span>⏱️ {readTime} min read</span>
              </div>
              <div className="flex items-center space-x-3">
                <span>👁️ {article.viewCount}</span>
                <span>❤️ {article.likeCount}</span>
              </div>
            </div>
          </div>
        </article>
      </Link>
    );
  }

  // Default variant
  return (
    <Link href={`/news/${article.slug}`} className={`block ${className}`}>
      <article className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
        <div className="flex space-x-4">
          {/* Image */}
          <div className="flex-shrink-0">
            <div className="relative w-20 h-20 md:w-24 md:h-24 overflow-hidden rounded-lg">
              <img
                src={imageUrl}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={(e) => handleImageError(e, 'News+Image')}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${categoryColors}`}>
                <span className="mr-1">
                  {categoryIcon}
                </span>
                {article.category?.name || 'General'}
              </span>
              {article.isFeatured && (
                <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">
                  Featured
                </span>
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
              {article.title}
            </h3>

            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {article.excerpt}
            </p>

            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-3">
                <span>📅 {timeAgo}</span>
                <span>⏱️ {readTime}m</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>👁️ {article.viewCount}</span>
                <span>❤️ {article.likeCount}</span>
              </div>
            </div>
          </div>
        </div>
      </article>
    </Link>
  );
};
