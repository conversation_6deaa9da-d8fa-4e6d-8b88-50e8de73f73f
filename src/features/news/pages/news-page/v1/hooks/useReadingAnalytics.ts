'use client';

import { useEffect, useRef, useCallback } from 'react';

interface ReadingAnalyticsData {
  articleId: string;
  title: string;
  category: string;
  timeSpent: number;
  scrollDepth: number;
  readingProgress: number;
}

interface UseReadingAnalyticsProps {
  articleId: string;
  title: string;
  category: string;
  onAnalytics?: (data: ReadingAnalyticsData) => void;
}

/**
 * Custom hook for tracking reading analytics
 * 
 * Tracks time spent, scroll depth, and reading progress
 * for news articles to provide insights on user engagement.
 */
export const useReadingAnalytics = ({
  articleId,
  title,
  category,
  onAnalytics
}: UseReadingAnalyticsProps) => {
  const startTimeRef = useRef<number>(Date.now());
  const maxScrollDepthRef = useRef<number>(0);
  const isVisibleRef = useRef<boolean>(true);
  const analyticsIntervalRef = useRef<NodeJS.Timeout>();

  // Calculate reading progress based on scroll position
  const calculateReadingProgress = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = scrollHeight > 0 ? Math.min((scrollTop / scrollHeight) * 100, 100) : 0;
    
    // Update max scroll depth
    if (progress > maxScrollDepthRef.current) {
      maxScrollDepthRef.current = progress;
    }
    
    return progress;
  }, []);

  // Send analytics data
  const sendAnalytics = useCallback(() => {
    if (!isVisibleRef.current) return;

    const timeSpent = Date.now() - startTimeRef.current;
    const readingProgress = calculateReadingProgress();
    
    const analyticsData: ReadingAnalyticsData = {
      articleId,
      title,
      category,
      timeSpent,
      scrollDepth: maxScrollDepthRef.current,
      readingProgress
    };

    // Call the analytics callback if provided
    if (onAnalytics) {
      onAnalytics(analyticsData);
    }

    // Send to analytics service (example)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'reading_progress', {
        article_id: articleId,
        article_title: title,
        category,
        time_spent: timeSpent,
        scroll_depth: maxScrollDepthRef.current,
        reading_progress: readingProgress
      });
    }

    // Console log for development
    console.log('📊 Reading Analytics:', analyticsData);
  }, [articleId, title, category, onAnalytics, calculateReadingProgress]);

  // Handle scroll events
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    const handleScroll = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateReadingProgress();
      }, 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [calculateReadingProgress]);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        isVisibleRef.current = false;
        sendAnalytics(); // Send analytics when user leaves
      } else {
        isVisibleRef.current = true;
        startTimeRef.current = Date.now(); // Reset start time when user returns
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [sendAnalytics]);

  // Periodic analytics reporting
  useEffect(() => {
    // Send analytics every 30 seconds while user is active
    analyticsIntervalRef.current = setInterval(() => {
      if (isVisibleRef.current) {
        sendAnalytics();
      }
    }, 30000);

    return () => {
      if (analyticsIntervalRef.current) {
        clearInterval(analyticsIntervalRef.current);
      }
    };
  }, [sendAnalytics]);

  // Send final analytics on unmount
  useEffect(() => {
    return () => {
      sendAnalytics();
    };
  }, [sendAnalytics]);

  // Reading milestones tracking
  const trackMilestone = useCallback((milestone: 25 | 50 | 75 | 100) => {
    const currentProgress = calculateReadingProgress();
    
    if (currentProgress >= milestone) {
      // Track milestone achievement
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'reading_milestone', {
          article_id: articleId,
          milestone,
          time_to_milestone: Date.now() - startTimeRef.current
        });
      }
      
      console.log(`📖 Reading milestone: ${milestone}% completed`);
    }
  }, [articleId, calculateReadingProgress]);

  // Auto-track milestones
  useEffect(() => {
    const checkMilestones = () => {
      const progress = calculateReadingProgress();
      
      // Track milestones
      if (progress >= 25) trackMilestone(25);
      if (progress >= 50) trackMilestone(50);
      if (progress >= 75) trackMilestone(75);
      if (progress >= 100) trackMilestone(100);
    };

    const interval = setInterval(checkMilestones, 5000);
    
    return () => clearInterval(interval);
  }, [trackMilestone, calculateReadingProgress]);

  return {
    sendAnalytics,
    calculateReadingProgress,
    trackMilestone
  };
};
