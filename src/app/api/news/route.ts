import { NextRequest, NextResponse } from 'next/server';
import {
  createErrorResponse,
  tryMultipleEndpoints,
  getBaseUrl,
  createSuccessResponse,
  parseQueryParams,
  buildQueryString,
  logAPIRequest
} from '@/features/news/utils/apiUtils';

// News API Proxy - Optimized with shared utilities

// Mock data generator for development with enhanced pagination
function generateMockNewsData(page: number, limit: number, category: string, search: string): NewsAPIResponse {
  const categories = [
    { id: 1, slug: 'breaking', name: 'Breaking', description: 'Breaking news', icon: '🔥', color: '#EF4444' },
    { id: 2, slug: 'match', name: 'Match', description: 'Match reports', icon: '⚽', color: '#3B82F6' },
    { id: 3, slug: 'transfer', name: 'Transfer', description: 'Transfer news', icon: '🔄', color: '#F59E0B' },
    { id: 4, slug: 'interview', name: 'Interview', description: 'Player interviews', icon: '🎤', color: '#8B5CF6' },
    { id: 5, slug: 'league', name: 'League', description: 'League updates', icon: '🏆', color: '#10B981' },
    { id: 6, slug: 'general', name: 'General', description: 'General news', icon: '📰', color: '#6B7280' }
  ];

  // Generate more mock articles for better pagination testing
  const baseArticles = [
    {
      id: 1,
      title: 'Manchester United Signs Mbappe in Record Breaking Deal',
      slug: 'manchester-united-signs-mbappe-record-deal',
      excerpt: 'In a stunning move that has shocked the football world, Manchester United has secured the signature of Kylian Mbappe in what is being called the transfer of the century.',
      content: 'Full article content here...',
      featuredImage: '/images/news/mbappe-united.jpg',
      tags: ['transfer', 'manchester-united', 'mbappe'],
      status: 'published',
      publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      viewCount: 15420,
      shareCount: 892,
      likeCount: 3456,
      isFeatured: true,
      priority: 9,
      category: categories[2], // transfer
      authorId: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 2,
      title: 'Liverpool Defeats Arsenal 3-1 in Premier League Thriller',
      slug: 'liverpool-defeats-arsenal-premier-league',
      excerpt: 'Liverpool secured a crucial victory against Arsenal at Anfield, with goals from Salah, Mane, and Firmino sealing a comprehensive 3-1 win.',
      content: 'Match report content here...',
      featuredImage: '/images/news/liverpool-arsenal.jpg',
      tags: ['match', 'liverpool', 'arsenal', 'premier-league'],
      status: 'published',
      publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      viewCount: 8930,
      shareCount: 445,
      likeCount: 1876,
      isFeatured: false,
      priority: 7,
      category: categories[1], // match
      authorId: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 3,
      title: 'Champions League Draw: Exciting Matchups Revealed',
      slug: 'champions-league-draw-exciting-matchups',
      excerpt: 'UEFA has announced the Champions League quarter-final draw, with several mouth-watering fixtures set to captivate football fans worldwide.',
      content: 'Draw analysis content here...',
      featuredImage: '/images/news/champions-league-draw.jpg',
      tags: ['champions-league', 'uefa', 'draw'],
      status: 'published',
      publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      viewCount: 12340,
      shareCount: 678,
      likeCount: 2341,
      isFeatured: true,
      priority: 8,
      category: categories[4], // league
      authorId: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 4,
      title: 'Exclusive: Ronaldo Opens Up About His Future Plans',
      slug: 'ronaldo-exclusive-interview-future-plans',
      excerpt: 'In an exclusive interview, Cristiano Ronaldo discusses his career, future ambitions, and what drives him to continue performing at the highest level.',
      content: 'Interview content here...',
      featuredImage: '/images/news/ronaldo-interview.jpg',
      tags: ['interview', 'ronaldo', 'exclusive'],
      status: 'published',
      publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
      viewCount: 18750,
      shareCount: 1234,
      likeCount: 4567,
      isFeatured: false,
      priority: 6,
      category: categories[3], // interview
      authorId: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 5,
      title: 'BREAKING: World Cup 2026 Venues Announced',
      slug: 'world-cup-2026-venues-announced',
      excerpt: 'FIFA has officially announced the host cities and venues for the 2026 World Cup, which will be jointly hosted by USA, Canada, and Mexico.',
      content: 'World Cup announcement content here...',
      featuredImage: '/images/news/world-cup-2026.jpg',
      tags: ['breaking', 'world-cup', 'fifa', '2026'],
      status: 'published',
      publishedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
      viewCount: 25680,
      shareCount: 1890,
      likeCount: 6789,
      isFeatured: true,
      priority: 10,
      category: categories[0], // breaking
      authorId: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  // Generate additional articles for pagination testing
  const mockArticles = [];
  for (let i = 0; i < 50; i++) {
    const baseIndex = i % baseArticles.length;
    const baseArticle = baseArticles[baseIndex];
    mockArticles.push({
      ...baseArticle,
      id: i + 1,
      title: `${baseArticle.title} - Article ${i + 1}`,
      slug: `${baseArticle.slug}-${i + 1}`,
      publishedAt: new Date(Date.now() - (i + 1) * 60 * 60 * 1000).toISOString(), // Hours ago
      viewCount: Math.floor(Math.random() * 20000) + 1000,
      shareCount: Math.floor(Math.random() * 1000) + 100,
      likeCount: Math.floor(Math.random() * 5000) + 500,
      category: categories[i % categories.length]
    });
  }

  // Filter by category if specified
  let filteredArticles = mockArticles;
  if (category && category !== 'all') {
    filteredArticles = mockArticles.filter(article =>
      article.category.slug === category ||
      article.category.name.toLowerCase() === category.toLowerCase()
    );
  }

  // Filter by search if specified
  if (search) {
    const searchLower = search.toLowerCase();
    filteredArticles = filteredArticles.filter(article =>
      article.title.toLowerCase().includes(searchLower) ||
      article.excerpt.toLowerCase().includes(searchLower) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  }

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedArticles = filteredArticles.slice(startIndex, endIndex);

  const totalItems = filteredArticles.length;
  const totalPages = Math.ceil(totalItems / limit);

  return {
    data: paginatedArticles,
    meta: {
      totalItems,
      totalPages,
      currentPage: page,
      limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null
    },
    status: 200
  };
}

interface NewsAPIResponse {
  data: any[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextPage: number | null;
    previousPage: number | null;
  };
  status: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const { page, limit, category, search } = parseQueryParams(searchParams);

    logAPIRequest('GET', '/api/news', { page, limit, category, search });

    // Build query string for backend
    const queryString = buildQueryString({
      page,
      limit,
      ...(category && category !== 'all' && { category }),
      ...(search && { search })
    });

    // Try multiple backend endpoints - Updated to match new API structure
    const baseUrl = getBaseUrl();
    const endpoints = [
      `${baseUrl}/news?${queryString}`,              // Primary: GET /news
      `${baseUrl}/public/news?${queryString}`,       // Public endpoint
      `${baseUrl}/news/articles?${queryString}`,     // Legacy fallback
      `${baseUrl}/news/published?${queryString}`     // Legacy fallback
    ];

    try {
      const response = await tryMultipleEndpoints(endpoints);
      const data: NewsAPIResponse = await response.json();

      if (data.data && data.data.length > 0) {
        console.log('✅ Backend news API successful, articles count:', data.data.length);
        return createSuccessResponse(data, 60);
      }
    } catch (endpointError) {
      console.log('⚠️ All backend endpoints failed');
      return createErrorResponse('News service unavailable', 503);
    }

    // If no data found, return empty result
    console.log('⚠️ No news data available from backend');
    return createSuccessResponse({
      data: [],
      meta: {
        currentPage: page,
        totalPages: 0,
        totalItems: 0,
        hasNextPage: false,
        hasPreviousPage: false
      }
    }, 30);

  } catch (error) {
    console.error('❌ News proxy error:', error);
    return createErrorResponse(error);
  }
}

// Optional: Add POST method for future news creation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const baseUrl = getBaseUrl();

    logAPIRequest('POST', '/api/news', { bodyKeys: Object.keys(body) });

    const response = await tryMultipleEndpoints([`${baseUrl}/news`], {
      method: 'POST',
      body: JSON.stringify(body),
      signal: AbortSignal.timeout(15000), // 15 seconds for POST
    });

    const data = await response.json();
    console.log('✅ News creation successful');

    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    });

  } catch (error) {
    console.error('❌ News creation proxy error:', error);
    return createErrorResponse(error);
  }
}

// Health check endpoint
export async function HEAD() {
  try {
    const response = await fetch(`${BASE_URL}/news?limit=1`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000),
    });

    return new NextResponse(null, {
      status: response.ok ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}
