# News API Updates - Public API Integration

## Overview
Đã cập nhật to<PERSON>n bộ hệ thống News API để tích hợp với các Public API endpoints mới theo cấu trúc được cung cấp.

## New API Endpoints Added

### 1. Featured News
- **Endpoint**: `GET /api/news/featured`
- **File**: `src/app/api/news/featured/route.ts`
- **Description**: Dedicated endpoint for fetching featured articles
- **Backend URLs**:
  - Primary: `GET /news/featured`
  - Public: `GET /public/news/featured`
  - Fallback: `GET /news?featured=true`

### 2. Category-based News
- **Endpoint**: `GET /api/news/category/[categorySlug]`
- **File**: `src/app/api/news/category/[categorySlug]/route.ts`
- **Description**: Fetch articles by category slug
- **Backend URLs**:
  - Primary: `GET /news/category/{categorySlug}`
  - Public: `GET /public/news/category/{categorySlug}`
  - Fallback: `GET /news?category={categorySlug}`

### 3. View Count Tracking
- **Endpoint**: `POST /api/news/[id]/view`
- **File**: `src/app/api/news/[id]/view/route.ts`
- **Description**: Increment article view count
- **Backend URLs**:
  - Primary: `POST /news/{id}/view`
  - Public: `POST /public/news/{id}/view`
  - Alternative: `POST /news/{id}/views`

### 4. Share Count Tracking
- **Endpoint**: `POST /api/news/[id]/share`
- **File**: `src/app/api/news/[id]/share/route.ts`
- **Description**: Increment article share count
- **Backend URLs**:
  - Primary: `POST /news/{id}/share`
  - Public: `POST /public/news/{id}/share`
  - Alternative: `POST /news/{id}/shares`

## Updated Existing Endpoints

### 1. Main News API
- **File**: `src/app/api/news/route.ts`
- **Changes**: Updated endpoint priority order to match new API structure
- **New Priority**:
  1. `GET /news`
  2. `GET /public/news`
  3. `GET /news/articles` (legacy)
  4. `GET /news/published` (legacy)

### 2. Article Detail API
- **File**: `src/app/api/news/article/[slug]/route.ts`
- **Changes**: Updated to prioritize new slug-based endpoints
- **New Priority**:
  1. `GET /news/{slug}`
  2. `GET /public/news/{slug}`
  3. Legacy endpoints as fallback

### 3. Related Articles API
- **File**: `src/app/api/news/related/[slug]/route.ts`
- **Changes**: Updated to use new related articles structure
- **New Priority**:
  1. `GET /news/{slug}/related`
  2. `GET /public/news/{slug}/related`
  3. Legacy endpoints as fallback

## Client Library Updates

### 1. News API Client (`src/lib/api/news.ts`)
**New Functions Added**:
- `fetchFeaturedNews()` - Uses dedicated featured endpoint with fallback
- `fetchNewsByCategory()` - Uses dedicated category endpoint with fallback
- `incrementViewCount()` - Increment article view count
- `incrementShareCount()` - Increment article share count

**Updated Functions**:
- `fetchFeaturedNews()` - Now uses `/api/news/featured` endpoint first
- `fetchNewsByCategory()` - Now uses `/api/news/category/{slug}` endpoint first

### 2. News Service (`src/features/news/services/NewsService.ts`)
**New Methods Added**:
- `incrementArticleViewCount()` - Service method for view tracking
- `incrementArticleShareCount()` - Service method for share tracking
- `invalidateArticleCache()` - Cache invalidation for updated articles

**Updated Methods**:
- `getArticlesByCategory()` - Now uses dedicated category endpoint with caching
- Import statements updated to include new functions

## API Structure Compatibility

### Request/Response Format
All endpoints maintain backward compatibility with existing response formats:

```typescript
interface NewsAPIResponse {
  data: NewsArticle[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
```

### Error Handling
- Multiple endpoint fallback strategy
- Graceful degradation to legacy endpoints
- Comprehensive error logging
- Consistent error response format

### Caching Strategy
- Featured articles: 60 seconds cache
- Category articles: 60 seconds cache
- View/Share counts: No cache (real-time updates)
- Health checks: No cache

## Backend Integration Points

### Environment Variables
Uses existing environment variables:
- `API_BASE_URL` or `BASE_URL` for backend URL
- `NEXT_PUBLIC_CDN_DOMAIN_PICTURE` for image URLs

### Authentication
All endpoints use the same authentication headers:
- `Content-Type: application/json`
- `User-Agent: Sports-Frontend-Proxy/1.0`

### Timeout Configuration
- Standard requests: 10 seconds timeout
- Health checks: 5 seconds timeout

## Testing & Monitoring

### Health Checks
All new endpoints include HEAD method for health monitoring:
- `/api/news/featured` - HEAD method available
- `/api/news/category/[categorySlug]` - HEAD method available
- `/api/news/[id]/view` - HEAD method available
- `/api/news/[id]/share` - HEAD method available

### Logging
Comprehensive logging for:
- API request/response cycles
- Endpoint fallback attempts
- Cache hit/miss statistics
- Error tracking and debugging

### Performance Optimization
- Automatic caching with TTL
- Multiple endpoint fallback
- Optimized query parameters
- Cache invalidation on data updates

## Migration Notes

### Backward Compatibility
- All existing API calls continue to work
- Legacy endpoints maintained as fallbacks
- No breaking changes to existing components

### Recommended Usage
- Use `newsService` methods for all news operations
- Leverage new dedicated endpoints for better performance
- Implement view/share tracking in article components
- Use category-specific endpoints for filtered content

## Next Steps

1. **Component Integration**: Update news components to use new tracking methods
2. **Analytics Integration**: Connect view/share tracking to analytics systems
3. **Performance Monitoring**: Monitor new endpoint performance
4. **Cache Optimization**: Fine-tune cache strategies based on usage patterns
5. **Error Monitoring**: Set up alerts for endpoint failures

## Files Modified

### New Files
- `src/app/api/news/featured/route.ts`
- `src/app/api/news/category/[categorySlug]/route.ts`
- `src/app/api/news/[id]/view/route.ts`
- `src/app/api/news/[id]/share/route.ts`

### Updated Files
- `src/app/api/news/route.ts`
- `src/app/api/news/article/[slug]/route.ts`
- `src/app/api/news/related/[slug]/route.ts`
- `src/lib/api/news.ts`
- `src/features/news/services/NewsService.ts`

All updates maintain full backward compatibility while providing enhanced functionality and better integration with the new Public API structure.
