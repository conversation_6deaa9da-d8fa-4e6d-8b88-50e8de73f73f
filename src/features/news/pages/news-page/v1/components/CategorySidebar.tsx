'use client';

import React from 'react';
import type { NewsCategory } from '@/lib/api/newsCategories';
import { mergeCategories, formatCategoryDisplayName } from '@/features/news/utils/categoryUtils';

interface CategorySidebarProps {
  categories: NewsCategory[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  isLoading?: boolean;
}

/**
 * Category Sidebar Component
 * 
 * Displays a list of news categories with icons and counts.
 * Allows users to filter news by category.
 */
export const CategorySidebar: React.FC<CategorySidebarProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  isLoading = false
}) => {
  // Use shared utility to merge categories
  const allCategories = mergeCategories(categories);





  // Loading skeleton
  if (isLoading) {
    return (
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
        <div className="space-y-2">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="flex items-center justify-between px-3 py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="w-8 h-4 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>

      <div className="space-y-2">
        {allCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.slug)}
            className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-colors duration-200 ${selectedCategory === category.slug
              ? 'bg-blue-50 text-blue-700 border border-blue-200'
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }`}
          >
            <div className="flex items-center space-x-3">
              <span className="text-lg">{category.icon}</span>
              <span className="font-medium">
                {formatCategoryDisplayName(category, false)}
              </span>
            </div>

            {category.count !== undefined && (
              <span className={`text-xs px-2 py-1 rounded-full ${selectedCategory === category.slug
                ? 'bg-blue-100 text-blue-600'
                : 'bg-gray-100 text-gray-500'
                }`}>
                {category.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Category Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          <div className="flex justify-between">
            <span>Total Articles</span>
            <span className="font-medium">
              {categories.reduce((sum, cat) => sum + (cat.count || 0), 0) || '---'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
